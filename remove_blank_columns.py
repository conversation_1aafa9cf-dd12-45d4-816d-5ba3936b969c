#!/usr/bin/env python3
"""
Simple Column Remover Script
============================
Remove columns that have blank/missing values, especially CT cycle time columns.

Usage: python remove_blank_columns.py
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def remove_blank_columns(input_file, output_file=None, remove_any_missing=True):
    """
    Remove columns that have blank/missing values.
    
    Parameters:
    -----------
    input_file : str
        Path to input CSV file
    output_file : str, optional
        Path to output CSV file 
    remove_any_missing : bool
        If True, remove any column with missing values
        If False, only remove columns that are mostly empty (>50% missing)
    """
    
    print("="*60)
    print("        REMOVE BLANK COLUMNS SCRIPT")
    print("="*60)
    
    # Load the data
    print(f"📂 Loading data from: {input_file}")
    try:
        df = pd.read_csv(input_file)
        print(f"✅ Data loaded successfully!")
        print(f"📊 Original shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return None
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Analyze missing values
    print(f"\n🔍 Analyzing missing values...")
    missing_info = []
    
    for col in df.columns:
        total_count = len(df)
        null_count = df[col].isnull().sum()
        missing_percentage = (null_count / total_count) * 100
        
        missing_info.append({
            'Column': col,
            'Missing_Count': null_count,
            'Missing_Percentage': missing_percentage,
            'Has_Missing': null_count > 0
        })
    
    missing_df = pd.DataFrame(missing_info)
    
    # Find columns to remove
    if remove_any_missing:
        # Remove any column with missing values
        columns_to_remove = missing_df[missing_df['Has_Missing']]['Column'].tolist()
        criteria_text = "any missing values"
    else:
        # Remove columns with >50% missing values
        columns_to_remove = missing_df[missing_df['Missing_Percentage'] > 50]['Column'].tolist()
        criteria_text = ">50% missing values"
    
    columns_to_keep = [col for col in df.columns if col not in columns_to_remove]
    
    print(f"\n📊 Analysis Results:")
    print(f"   Removal criteria: {criteria_text}")
    print(f"   Total columns: {len(df.columns)}")
    print(f"   Columns with missing data: {missing_df['Has_Missing'].sum()}")
    print(f"   Columns to remove: {len(columns_to_remove)}")
    print(f"   Columns to keep: {len(columns_to_keep)}")
    
    # Show columns being removed
    if columns_to_remove:
        print(f"\n🗑️  COLUMNS BEING REMOVED:")
        print(f"{'Column Name':<35} {'Missing Count':<15} {'Missing %':<12}")
        print(f"{'-'*35} {'-'*15} {'-'*12}")
        
        for col in columns_to_remove:
            row = missing_df[missing_df['Column'] == col].iloc[0]
            col_display = col[:32] + "..." if len(col) > 35 else col
            print(f"{col_display:<35} {row['Missing_Count']:<15,} {row['Missing_Percentage']:<11.1f}%")
        
        # Specifically highlight CT columns
        ct_columns = [col for col in columns_to_remove if 'CT_' in col or 'cycle' in col.lower() or 'time' in col.lower()]
        if ct_columns:
            print(f"\n⏱️  CT/Cycle Time columns being removed:")
            for col in ct_columns:
                row = missing_df[missing_df['Column'] == col].iloc[0]
                print(f"   • {col} ({row['Missing_Percentage']:.1f}% missing)")
    
    # Create cleaned dataset
    df_cleaned = df[columns_to_keep].copy()
    
    print(f"\n📈 RESULTS:")
    print(f"   Original: {df.shape[0]:,} rows × {df.shape[1]} columns")
    print(f"   Cleaned:  {df_cleaned.shape[0]:,} rows × {df_cleaned.shape[1]} columns")
    print(f"   Removed:  {len(columns_to_remove)} columns")
    print(f"   Retention: {(len(columns_to_keep) / len(df.columns) * 100):.1f}% of columns")
    
    # Check remaining missing values
    remaining_missing = df_cleaned.isnull().sum().sum()
    print(f"   Remaining missing values: {remaining_missing:,}")
    
    # Generate output filename if not provided
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = f"{base_name}_no_blanks_{timestamp}.csv"
    
    # Save cleaned data
    try:
        df_cleaned.to_csv(output_file, index=False)
        file_size = os.path.getsize(output_file) / 1024**2
        print(f"\n💾 Cleaned data saved to: {output_file}")
        print(f"📁 File size: {file_size:.2f} MB")
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return df_cleaned
    
    # Show remaining columns summary
    print(f"\n📋 REMAINING COLUMNS SUMMARY:")
    print(f"{'Column Name':<35} {'Data Type':<12} {'Non-Null Count':<15}")
    print(f"{'-'*35} {'-'*12} {'-'*15}")
    
    for col in df_cleaned.columns[:10]:  # Show first 10
        dtype = str(df_cleaned[col].dtype)
        non_null = df_cleaned[col].count()
        col_display = col[:32] + "..." if len(col) > 35 else col
        print(f"{col_display:<35} {dtype:<12} {non_null:<15,}")
    
    if len(df_cleaned.columns) > 10:
        print(f"   ... and {len(df_cleaned.columns) - 10} more columns")
    
    print(f"\n✅ Column removal completed successfully!")
    print("="*60)
    
    return df_cleaned

def main():
    """Main function to run the column removal script."""
    
    # Configuration
    INPUT_FILE = "20250613160303_CELONIS_EXPORT.csv"
    
    # Check if input file exists
    if not os.path.exists(INPUT_FILE):
        print(f"❌ Error: Input file '{INPUT_FILE}' not found!")
        print(f"📁 Available CSV files:")
        for file in os.listdir('.'):
            if file.endswith('.csv'):
                print(f"   • {file}")
        return
    
    print("Choose removal strategy:")
    print("1. Remove ANY column with missing values (strict)")
    print("2. Remove columns with >50% missing values (moderate)")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            remove_any_missing = True
            print("🎯 Using STRICT strategy: removing any column with missing values")
        elif choice == "2":
            remove_any_missing = False
            print("🎯 Using MODERATE strategy: removing columns with >50% missing values")
        else:
            print("❌ Invalid choice. Using moderate strategy as default.")
            remove_any_missing = False
    except:
        print("🎯 Using MODERATE strategy as default")
        remove_any_missing = False
    
    # Run the column removal
    cleaned_df = remove_blank_columns(
        input_file=INPUT_FILE,
        remove_any_missing=remove_any_missing
    )
    
    if cleaned_df is not None:
        print(f"\n🎉 Script completed successfully!")
        print(f"📊 Use the cleaned dataset for your analysis.")
        
        # Quick preview of remaining columns
        print(f"\n📋 Remaining columns ({len(cleaned_df.columns)}):")
        for i, col in enumerate(cleaned_df.columns, 1):
            print(f"   {i:2d}. {col}")
    else:
        print(f"\n❌ Script failed!")

if __name__ == "__main__":
    main()
