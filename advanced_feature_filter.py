#!/usr/bin/env python3
"""
Advanced Feature Filter and Analyzer Script
==========================================
Enhanced script for filtering O2C data features with advanced options.

Features:
- Multiple filtering strategies
- Data quality assessment
- Feature correlation analysis
- Export reports
- Configurable thresholds

Usage Examples:
    python advanced_feature_filter.py
    python advanced_feature_filter.py --threshold 0.1 --strategy strict
    python advanced_feature_filter.py --input data.csv --output filtered.csv
"""

import pandas as pd
import numpy as np
import os
import argparse
from datetime import datetime
import json

class AdvancedFeatureFilter:
    """Advanced feature filtering and analysis class."""
    
    def __init__(self, min_non_null_percentage=0.05, strategy='balanced'):
        """
        Initialize the feature filter.
        
        Parameters:
        -----------
        min_non_null_percentage : float
            Minimum percentage of non-null values to keep a column
        strategy : str
            Filtering strategy: 'strict', 'balanced', or 'permissive'
        """
        self.min_non_null_percentage = min_non_null_percentage
        self.strategy = strategy
        self.original_df = None
        self.filtered_df = None
        self.filter_report = {}
        
        # Set strategy-specific thresholds
        self.strategy_thresholds = {
            'strict': {'min_pct': 0.8, 'max_unique_pct': 0.95},
            'balanced': {'min_pct': 0.1, 'max_unique_pct': 0.9},
            'permissive': {'min_pct': 0.01, 'max_unique_pct': 0.99}
        }
    
    def load_data(self, input_file):
        """Load data from CSV file."""
        print(f"📂 Loading data from: {input_file}")
        try:
            self.original_df = pd.read_csv(input_file)
            # Clean column names
            self.original_df.columns = self.original_df.columns.str.strip()
            print(f"✅ Data loaded: {self.original_df.shape[0]:,} rows × {self.original_df.shape[1]} columns")
            return True
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return False
    
    def analyze_data_quality(self):
        """Comprehensive data quality analysis."""
        print(f"\n🔍 COMPREHENSIVE DATA QUALITY ANALYSIS")
        print("="*55)
        
        if self.original_df is None:
            print("❌ No data loaded!")
            return None
        
        quality_report = []
        
        for col in self.original_df.columns:
            total_count = len(self.original_df)
            non_null_count = self.original_df[col].count()
            null_count = self.original_df[col].isnull().sum()
            non_null_pct = (non_null_count / total_count) * 100
            unique_count = self.original_df[col].nunique()
            unique_pct = (unique_count / total_count) * 100 if total_count > 0 else 0
            
            # Data type analysis
            dtype = str(self.original_df[col].dtype)
            is_numeric = self.original_df[col].dtype in ['int64', 'float64']
            is_categorical = self.original_df[col].dtype == 'object'
            
            # Quality metrics
            if is_numeric and non_null_count > 0:
                has_negative = (self.original_df[col] < 0).any()
                has_zero = (self.original_df[col] == 0).any()
                mean_val = self.original_df[col].mean()
                std_val = self.original_df[col].std()
                skewness = self.original_df[col].skew()
            else:
                has_negative = False
                has_zero = False
                mean_val = None
                std_val = None
                skewness = None
            
            quality_info = {
                'Column': col,
                'Data_Type': dtype,
                'Total_Records': total_count,
                'Non_Null_Count': non_null_count,
                'Null_Count': null_count,
                'Non_Null_Percentage': non_null_pct,
                'Unique_Count': unique_count,
                'Unique_Percentage': unique_pct,
                'Is_Numeric': is_numeric,
                'Is_Categorical': is_categorical,
                'Has_Negative': has_negative,
                'Has_Zero': has_zero,
                'Mean': mean_val,
                'Std': std_val,
                'Skewness': skewness
            }
            
            quality_report.append(quality_info)
        
        self.quality_df = pd.DataFrame(quality_report)
        return self.quality_df
    
    def apply_filtering_strategy(self):
        """Apply the selected filtering strategy."""
        if self.quality_df is None:
            print("❌ Run data quality analysis first!")
            return None
        
        strategy_config = self.strategy_thresholds.get(self.strategy, self.strategy_thresholds['balanced'])
        min_pct = strategy_config['min_pct'] * 100
        max_unique_pct = strategy_config['max_unique_pct'] * 100
        
        print(f"\n🎯 APPLYING '{self.strategy.upper()}' FILTERING STRATEGY")
        print("="*55)
        print(f"📊 Minimum non-null percentage: {min_pct:.1f}%")
        print(f"📊 Maximum unique percentage: {max_unique_pct:.1f}%")
        
        # Apply filters
        conditions = [
            self.quality_df['Non_Null_Percentage'] >= min_pct,
            self.quality_df['Unique_Percentage'] <= max_unique_pct
        ]
        
        # Additional strategy-specific filters
        if self.strategy == 'strict':
            # Remove columns with too many unique values (likely IDs)
            conditions.append(self.quality_df['Unique_Count'] < len(self.original_df) * 0.8)
        
        # Combine all conditions
        filter_mask = pd.concat(conditions, axis=1).all(axis=1)
        columns_to_keep = self.quality_df[filter_mask]['Column'].tolist()
        columns_to_remove = self.quality_df[~filter_mask]['Column'].tolist()
        
        print(f"\n📈 Filtering Results:")
        print(f"✅ Columns to keep: {len(columns_to_keep)}")
        print(f"❌ Columns to remove: {len(columns_to_remove)}")
        
        if columns_to_remove:
            print(f"\n🗑️  Removed columns:")
            for col in columns_to_remove[:10]:  # Show first 10
                row = self.quality_df[self.quality_df['Column'] == col].iloc[0]
                reason = []
                if row['Non_Null_Percentage'] < min_pct:
                    reason.append(f"Low data ({row['Non_Null_Percentage']:.1f}%)")
                if row['Unique_Percentage'] > max_unique_pct:
                    reason.append(f"Too unique ({row['Unique_Percentage']:.1f}%)")
                
                print(f"   • {col}: {', '.join(reason)}")
            
            if len(columns_to_remove) > 10:
                print(f"   ... and {len(columns_to_remove) - 10} more columns")
        
        # Create filtered dataset
        self.filtered_df = self.original_df[columns_to_keep].copy()
        
        # Generate filter report
        self.filter_report = {
            'strategy': self.strategy,
            'original_columns': self.original_df.shape[1],
            'filtered_columns': self.filtered_df.shape[1],
            'columns_removed': len(columns_to_remove),
            'removal_percentage': (len(columns_to_remove) / self.original_df.shape[1]) * 100,
            'criteria': {
                'min_non_null_percentage': min_pct,
                'max_unique_percentage': max_unique_pct
            },
            'removed_columns': columns_to_remove
        }
        
        return self.filtered_df
    
    def generate_feature_analysis(self):
        """Generate analysis of filtered features."""
        if self.filtered_df is None:
            print("❌ No filtered data available!")
            return None
        
        print(f"\n📊 FILTERED DATASET ANALYSIS")
        print("="*40)
        
        # Basic statistics
        print(f"📈 Shape: {self.filtered_df.shape[0]:,} rows × {self.filtered_df.shape[1]} columns")
        
        # Feature types
        numerical_cols = self.filtered_df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = self.filtered_df.select_dtypes(include=['object']).columns.tolist()
        
        print(f"🔢 Numerical features: {len(numerical_cols)}")
        print(f"📝 Categorical features: {len(categorical_cols)}")
        
        # Target variable analysis (if exists)
        target_candidates = ['IsLate_Billed', 'Late_Billed', 'Target', 'Label']
        target_col = None
        
        for candidate in target_candidates:
            if candidate in self.filtered_df.columns:
                target_col = candidate
                break
        
        if target_col:
            print(f"\n🎯 Target Variable Found: {target_col}")
            target_dist = self.filtered_df[target_col].value_counts()
            print(f"   Distribution: {dict(target_dist)}")
        
        # Missing values in filtered data
        missing_summary = self.filtered_df.isnull().sum()
        columns_with_missing = missing_summary[missing_summary > 0]
        
        if len(columns_with_missing) > 0:
            print(f"\n⚠️  Columns still have missing values:")
            for col, missing_count in columns_with_missing.items():
                missing_pct = (missing_count / len(self.filtered_df)) * 100
                print(f"   • {col}: {missing_count} ({missing_pct:.1f}%)")
        else:
            print(f"\n✅ No missing values in filtered dataset!")
        
        return {
            'shape': self.filtered_df.shape,
            'numerical_features': numerical_cols,
            'categorical_features': categorical_cols,
            'target_column': target_col,
            'missing_values': dict(columns_with_missing) if len(columns_with_missing) > 0 else {}
        }
    
    def save_results(self, output_file=None, save_report=True):
        """Save filtered data and generate reports."""
        if self.filtered_df is None:
            print("❌ No filtered data to save!")
            return None
        
        # Generate output filename if not provided
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"filtered_features_{self.strategy}_{timestamp}.csv"
        
        try:
            # Save filtered data
            self.filtered_df.to_csv(output_file, index=False)
            file_size = os.path.getsize(output_file) / 1024**2
            print(f"\n💾 Filtered data saved to: {output_file}")
            print(f"📁 File size: {file_size:.2f} MB")
            
            # Save detailed report
            if save_report:
                report_file = output_file.replace('.csv', '_report.json')
                with open(report_file, 'w') as f:
                    json.dump(self.filter_report, f, indent=2)
                print(f"📋 Filter report saved to: {report_file}")
                
                # Save quality analysis
                quality_file = output_file.replace('.csv', '_quality_analysis.csv')
                self.quality_df.to_csv(quality_file, index=False)
                print(f"📊 Quality analysis saved to: {quality_file}")
            
            return output_file
            
        except Exception as e:
            print(f"❌ Error saving files: {e}")
            return None

def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description='Advanced Feature Filter for O2C Data')
    parser.add_argument('--input', '-i', default='20250613160303_CELONIS_EXPORT.csv',
                       help='Input CSV file path')
    parser.add_argument('--output', '-o', default=None,
                       help='Output CSV file path (auto-generated if not specified)')
    parser.add_argument('--threshold', '-t', type=float, default=0.05,
                       help='Minimum non-null percentage (0.0-1.0)')
    parser.add_argument('--strategy', '-s', choices=['strict', 'balanced', 'permissive'],
                       default='balanced', help='Filtering strategy')
    parser.add_argument('--no-report', action='store_true',
                       help='Skip generating detailed reports')
    
    args = parser.parse_args()
    
    print("="*70)
    print("         ADVANCED FEATURE FILTER FOR O2C DATA")
    print("="*70)
    print(f"📂 Input file: {args.input}")
    print(f"🎯 Strategy: {args.strategy}")
    print(f"📊 Threshold: {args.threshold*100:.1f}%")
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"❌ Error: Input file '{args.input}' not found!")
        return
    
    # Initialize filter
    filter_tool = AdvancedFeatureFilter(
        min_non_null_percentage=args.threshold,
        strategy=args.strategy
    )
    
    # Execute filtering pipeline
    if not filter_tool.load_data(args.input):
        return
    
    filter_tool.analyze_data_quality()
    filter_tool.apply_filtering_strategy()
    filter_tool.generate_feature_analysis()
    
    # Save results
    output_file = filter_tool.save_results(
        output_file=args.output,
        save_report=not args.no_report
    )
    
    if output_file:
        print(f"\n🎉 Feature filtering completed successfully!")
        print(f"📊 Use '{output_file}' for your EDA and modeling.")
        print("="*70)
    else:
        print(f"\n❌ Feature filtering failed!")

if __name__ == "__main__":
    main()
