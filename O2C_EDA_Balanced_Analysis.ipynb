# Import Required Libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from scipy import stats
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Configuration
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)

print("📚 All libraries imported successfully!")
print("🎯 Ready for EDA on balanced sample data!")

# Load the balanced sample data
print("🔄 Loading balanced sample data...")

# Load CSV file using pandas
df_eda = pd.read_csv('On-time_billing.csv')

print(f"✅ Data loaded successfully!")
print(f"📊 Dataset Shape: {df_eda.shape[0]:,} rows × {df_eda.shape[1]} columns")
print(f"💾 Memory Usage: {df_eda.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

print("\n" + "="*60)
print("                    FIRST 5 ROWS")
print("="*60)
display(df_eda.head())

print("\n" + "="*60)
print("                    LAST 5 ROWS")
print("="*60)
display(df_eda.tail())

# Data Cleaning Steps
print("🧹 Starting Data Cleaning...")

# Check current data types
print("📋 Current Data Types:")
print(df_eda.dtypes)

print("\n" + "="*50)
print("         CLEANING OPERATIONS")
print("="*50)

# 1. Convert IsLate_Billed from string to integer
print("🔄 Converting 'IsLate_Billed' to integer...")
print(f"   Before: {df_eda['IsLate_Billed'].dtype}")

# Check unique values first
print(f"   Unique values: {df_eda['IsLate_Billed'].unique()}")

# Convert to integer
df_eda['IsLate_Billed'] = df_eda['IsLate_Billed'].astype(int)
print(f"   After: {df_eda['IsLate_Billed'].dtype}")

# 2. Drop CASE_KEY (ID column not needed for modeling)
print("\n🗑️  Dropping ID column 'CASE_KEY'...")
print(f"   Columns before drop: {df_eda.shape[1]}")

if 'CASE_KEY' in df_eda.columns:
    df_eda = df_eda.drop('CASE_KEY', axis=1)
    print(f"   ✅ CASE_KEY dropped successfully")
else:
    print(f"   ℹ️  CASE_KEY column not found")

print(f"   Columns after drop: {df_eda.shape[1]}")

# 3. Clean column names (remove extra spaces)
print("\n🔧 Cleaning column names...")
original_cols = df_eda.columns.tolist()
df_eda.columns = df_eda.columns.str.strip()
cleaned_cols = df_eda.columns.tolist()

if original_cols != cleaned_cols:
    print("   ✅ Column names cleaned (spaces removed)")
else:
    print("   ℹ️  Column names already clean")

print(f"\n✅ Data cleaning completed!")
print(f"📊 Final dataset shape: {df_eda.shape[0]:,} rows × {df_eda.shape[1]} columns")

# Display cleaned data types
print("\n📋 Updated Data Types:")
print(df_eda.dtypes)

# =============================================================================
# COLUMN TYPE CORRECTIONS AND DATA CLEANING
# =============================================================================
print("🔧 Applying column type corrections and data cleaning...")

# 1. Define identifier columns (exclude from modeling)
identifier_cols = ['VBELN', 'POSNR']
print(f"📝 Identifier columns (excluded from modeling): {identifier_cols}")

# 2. Define target variable
target_variable = 'IsLate_Billed'
print(f"🎯 Target variable: {target_variable}")

# 3. Convert block columns from 0/1 numeric to categorical
block_columns = ['Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']
existing_block_cols = [col for col in block_columns if col in df_eda.columns]

if existing_block_cols:
    print(f"🔄 Converting block columns to categorical: {existing_block_cols}")
    for col in existing_block_cols:
        # Convert 0/1 to categorical labels
        df_eda[col] = df_eda[col].map({0: 'No', 1: 'Yes'}).astype('category')
        print(f"   ✅ {col}: {df_eda[col].dtype} (categories: {list(df_eda[col].cat.categories)})")
else:
    print("ℹ️ No block columns found in dataset")

# 4. Identify CT (cycle time) columns
ct_columns = [col for col in df_eda.columns if 'CT' in col]
print(f"⏱️ Cycle Time (CT) columns identified: {ct_columns}")

# 5. Remove rows with missing values in any CT column
if ct_columns:
    initial_rows = len(df_eda)
    print(f"📊 Initial dataset size: {initial_rows} rows")
    
    # Check missing values in CT columns before removal
    ct_missing_before = df_eda[ct_columns].isnull().sum()
    total_ct_missing = ct_missing_before.sum()
    
    if total_ct_missing > 0:
        print(f"🔍 Missing values in CT columns before removal:")
        for col in ct_columns:
            if ct_missing_before[col] > 0:
                print(f"   {col}: {ct_missing_before[col]} missing ({ct_missing_before[col]/initial_rows*100:.1f}%)")
        
        # Remove rows with any missing CT values
        df_eda = df_eda.dropna(subset=ct_columns)
        
        final_rows = len(df_eda)
        rows_removed = initial_rows - final_rows
        print(f"✂️ Removed {rows_removed} rows with missing CT values ({rows_removed/initial_rows*100:.1f}%)")
        print(f"📊 Final dataset size: {final_rows} rows")
        
        # Verify no missing CT values remain
        ct_missing_after = df_eda[ct_columns].isnull().sum().sum()
        print(f"✅ Verification: {ct_missing_after} missing CT values remaining")
    else:
        print("✅ No missing values found in CT columns")
else:
    print("⚠️ No CT columns found in dataset")

# 6. Update column categorization after changes
print("\n🔄 Updating column categorization...")

# Exclude identifiers and target from feature columns
feature_columns = [col for col in df_eda.columns if col not in identifier_cols + [target_variable]]
print(f"📊 Feature columns (excluding identifiers and target): {len(feature_columns)} columns")

# Re-identify numerical and categorical columns
numerical_cols = []
categorical_cols = []

for col in feature_columns:
    if df_eda[col].dtype in ['int64', 'float64'] and col not in existing_block_cols:
        numerical_cols.append(col)
    else:
        categorical_cols.append(col)

print(f"🔢 Numerical columns: {len(numerical_cols)}")
print(f"🏷️ Categorical columns: {len(categorical_cols)}")

# Display updated data info
print("\n" + "="*60)
print("UPDATED DATASET SUMMARY")
print("="*60)
print(f"Shape: {df_eda.shape}")
print(f"Identifier columns: {len(identifier_cols)}")
print(f"Target variable: 1 ({target_variable})")
print(f"Feature columns: {len(feature_columns)}")
print(f"Numerical features: {len(numerical_cols)}")
print(f"Categorical features: {len(categorical_cols)}")

print("\n✅ Column type corrections and data cleaning completed!")

# Identify Numerical and Categorical Columns
print("🔍 Identifying Column Types...")

# =============================================================================
# UPDATED COLUMN IDENTIFICATION AND DATA TYPES
# =============================================================================
print("📊 Updated Column Analysis and Data Types")
print("="*60)

# Use the correct target variable name
target_variable = 'IsLate_Billed'
print(f"🎯 Target Variable: {target_variable}")

# Identifier columns (excluded from modeling)
print(f"\n🔑 Identifier Columns ({len(identifier_cols)}):")
for i, col in enumerate(identifier_cols, 1):
    print(f"   {i}. {col}")

# Feature columns (excluding identifiers and target)
feature_cols_for_analysis = [col for col in df_eda.columns if col not in identifier_cols + [target_variable]]
print(f"\n📈 Feature Columns for Analysis ({len(feature_cols_for_analysis)}):")

# Numerical columns
print(f"\n🔢 Numerical Columns ({len(numerical_cols)}):")
for i, col in enumerate(numerical_cols, 1):
    print(f"   {i}. {col} ({df_eda[col].dtype})")

# Categorical columns  
print(f"\n🏷️ Categorical Columns ({len(categorical_cols)}):")
for i, col in enumerate(categorical_cols, 1):
    unique_count = df_eda[col].nunique()
    if df_eda[col].dtype.name == 'category':
        print(f"   {i}. {col} ({df_eda[col].dtype}) - {unique_count} categories")
    else:
        print(f"   {i}. {col} ({df_eda[col].dtype}) - {unique_count} unique values")

# Data type summary
print(f"\n📋 Data Types Summary:")
print(f"   • Total columns: {len(df_eda.columns)}")
print(f"   • Identifier columns: {len(identifier_cols)}")
print(f"   • Target variable: 1")
print(f"   • Feature columns: {len(feature_cols_for_analysis)}")
print(f"     - Numerical: {len(numerical_cols)}")
print(f"     - Categorical: {len(categorical_cols)}")

# Show specific data types
print(f"\n🔍 Detailed Data Types:")
dtype_summary = df_eda.dtypes.value_counts()
for dtype, count in dtype_summary.items():
    print(f"   • {dtype}: {count} columns")

print("\n✅ Column identification completed with proper data types!")

# Basic Data Overview
print("📊 COMPREHENSIVE DATA OVERVIEW")
print("="*60)

# 1. Dataset Shape
print(f"📏 Dataset Shape: {df_eda.shape[0]:,} rows × {df_eda.shape[1]} columns")
print(f"💾 Memory Usage: {df_eda.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# 2. Data Info
print(f"\n📋 DATA INFO:")
print("="*40)
df_eda.info()

# 3. Descriptive Statistics for Numerical Columns
print(f"\n📈 NUMERICAL COLUMNS STATISTICS:")
print("="*40)
if len(numerical_cols) > 0:
    numerical_stats = df_eda[numerical_cols].describe()
    display(numerical_stats)
    
    # Additional statistics
    print(f"\n📊 Additional Statistics:")
    for col in numerical_cols:
        skewness = df_eda[col].skew()
        kurtosis = df_eda[col].kurtosis()
        print(f"   {col}: Skewness={skewness:.3f}, Kurtosis={kurtosis:.3f}")
else:
    print("   No numerical columns found")

# 4. Value Counts for Categorical Columns
print(f"\n📝 CATEGORICAL COLUMNS VALUE COUNTS:")
print("="*40)
if len(categorical_cols) > 0:
    for col in categorical_cols:
        print(f"\n🔹 {col}:")
        value_counts = df_eda[col].value_counts()
        print(value_counts.head(10))  # Show top 10 categories
        
        if len(value_counts) > 10:
            print(f"   ... and {len(value_counts) - 10} more categories")
        
        # Show percentage distribution
        value_pcts = df_eda[col].value_counts(normalize=True) * 100
        print(f"   Top category: {value_pcts.iloc[0]:.1f}%")
else:
    print("   No categorical columns found")

# 5. Data Quality Summary
print(f"\n🔍 DATA QUALITY SUMMARY:")
print("="*40)
print(f"✅ Total Records: {len(df_eda):,}")
print(f"✅ Total Features: {len(df_eda.columns) - 1}")  # Excluding target
print(f"✅ Numerical Features: {len(numerical_cols)}")
print(f"✅ Categorical Features: {len(categorical_cols)}")
print(f"✅ Target Variable: {target_variable}")

# =============================================================================
# MISSING VALUES ANALYSIS (AFTER DATA CLEANING)
# =============================================================================
print("🔍 Missing Values Analysis (After CT Row Removal)")
print("="*60)

# Calculate missing values
missing_values = df_eda.isnull().sum()
missing_percentage = (missing_values / len(df_eda)) * 100

# Create missing values summary
missing_df = pd.DataFrame({
    'Column': df_eda.columns,
    'Missing_Count': missing_values,
    'Missing_Percentage': missing_percentage,
    'Data_Type': df_eda.dtypes,
    'Column_Type': ['Identifier' if col in identifier_cols 
                   else 'Target' if col == target_variable
                   else 'Numerical' if col in numerical_cols
                   else 'Categorical' for col in df_eda.columns]
})

# Sort by missing percentage
missing_df = missing_df.sort_values('Missing_Percentage', ascending=False)

# Display missing values summary
print(f"📊 Dataset Shape: {df_eda.shape}")
print(f"🔍 Total Missing Values: {missing_values.sum():,}")
print(f"📈 Overall Missing Percentage: {(missing_values.sum() / (len(df_eda) * len(df_eda.columns))) * 100:.2f}%")

# Show columns with missing values
columns_with_missing = missing_df[missing_df['Missing_Count'] > 0]
if len(columns_with_missing) > 0:
    print(f"\n❌ Columns with Missing Values ({len(columns_with_missing)}):")
    print(columns_with_missing[['Column', 'Missing_Count', 'Missing_Percentage', 'Column_Type']].to_string(index=False))
else:
    print("\n✅ No missing values found in any column!")

# Verify CT columns have no missing values
ct_columns = [col for col in df_eda.columns if 'CT' in col]
ct_missing = df_eda[ct_columns].isnull().sum()
print(f"\n⏱️ CT Columns Missing Values Verification:")
for col in ct_columns:
    status = "✅ Clean" if ct_missing[col] == 0 else f"❌ {ct_missing[col]} missing"
    print(f"   {col}: {status}")

# Missing values by column type
print(f"\n📊 Missing Values by Column Type:")
missing_by_type = missing_df.groupby('Column_Type').agg({
    'Missing_Count': ['count', 'sum'],
    'Missing_Percentage': 'mean'
}).round(2)
missing_by_type.columns = ['Total_Columns', 'Total_Missing', 'Avg_Missing_Pct']
print(missing_by_type)

# Data completeness summary
total_cells = len(df_eda) * len(df_eda.columns)
complete_cells = total_cells - missing_values.sum()
completeness_percentage = (complete_cells / total_cells) * 100

print(f"\n📈 Data Completeness Summary:")
print(f"   • Total data points: {total_cells:,}")
print(f"   • Complete data points: {complete_cells:,}")
print(f"   • Data completeness: {completeness_percentage:.2f}%")

print("\n✅ Missing values analysis completed!")

# Quick check of column names and target variable
print("📋 All column names in dataset:")
print(list(df_eda.columns))
print(f"\nLooking for target variable (late billing indicator)...")

# Check for potential target variable names
potential_targets = [col for col in df_eda.columns if any(keyword in col.lower() for keyword in ['late', 'bill', 'target', 'label'])]
print(f"Potential target columns: {potential_targets}")

# Check unique values in the last column (often the target)
last_col = df_eda.columns[-1]
print(f"\nLast column '{last_col}' unique values: {sorted(df_eda[last_col].unique())}")

# Update target variable name
if 'IsLate_Billed' in df_eda.columns:
    target_variable = 'IsLate_Billed'
    print(f"✅ Target variable identified: {target_variable}")
else:
    print("⚠️ Target variable needs to be identified manually")

# =============================================================================
# TARGET VARIABLE ANALYSIS
# =============================================================================
print("🎯 TARGET VARIABLE ANALYSIS")
print("="*50)

# Use the correct target variable name
target_variable = 'IsLate_Billed'

# Basic statistics
target_counts = df_eda[target_variable].value_counts().sort_index()
target_percentages = df_eda[target_variable].value_counts(normalize=True).sort_index() * 100

print(f"📊 Target Variable: {target_variable}")
print(f"📏 Dataset Shape after cleaning: {df_eda.shape}")

# Display target distribution
print(f"\n📈 Target Distribution:")
for value, count in target_counts.items():
    percentage = target_percentages[value]
    label = "On-time Billing" if value == 0 else "Late Billing"
    print(f"   {value} ({label}): {count:,} cases ({percentage:.1f}%)")

# Calculate class balance
balance_ratio = target_counts.min() / target_counts.max()
print(f"\n⚖️ Class Balance Ratio: {balance_ratio:.3f}")

# Determine balance status
if balance_ratio >= 0.8:
    balance_status = "Well Balanced ✅"
elif balance_ratio >= 0.5:
    balance_status = "Moderately Balanced ⚠️"
else:
    balance_status = "Imbalanced ❌"

print(f"📊 Balance Status: {balance_status}")

# Visualization
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Count plot
target_counts.plot(kind='bar', ax=ax1, color=['lightblue', 'lightcoral'])
ax1.set_title(f'Target Variable Distribution\n({target_variable})')
ax1.set_xlabel('Target Value')
ax1.set_ylabel('Count')
ax1.set_xticklabels(['On-time (0)', 'Late (1)'], rotation=0)

# Add count labels on bars
for i, v in enumerate(target_counts.values):
    ax1.text(i, v + 20, f'{v:,}', ha='center', fontweight='bold')

# Pie chart
labels = ['On-time Billing', 'Late Billing']
colors = ['lightblue', 'lightcoral']
wedges, texts, autotexts = ax2.pie(target_counts.values, labels=labels, colors=colors, 
                                  autopct='%1.1f%%', startangle=90)
ax2.set_title(f'Target Variable Proportion\n({target_variable})')

# Make percentage text bold
for autotext in autotexts:
    autotext.set_fontweight('bold')

plt.tight_layout()
plt.show()

print(f"\n✅ Target variable analysis completed!")

# =============================================================================
# MISSING VALUES ANALYSIS (AFTER DATA CLEANING)
# =============================================================================
print("🔍 Missing Values Analysis (After CT Row Removal)")
print("="*60)

# Calculate missing values
missing_values = df_eda.isnull().sum()
missing_percentage = (missing_values / len(df_eda)) * 100

# Create missing values summary
missing_df = pd.DataFrame({
    'Column': df_eda.columns,
    'Missing_Count': missing_values,
    'Missing_Percentage': missing_percentage,
    'Data_Type': df_eda.dtypes,
    'Column_Type': ['Identifier' if col in identifier_cols 
                   else 'Target' if col == target_variable
                   else 'Numerical' if col in numerical_cols
                   else 'Categorical' for col in df_eda.columns]
})

# Sort by missing percentage
missing_df = missing_df.sort_values('Missing_Percentage', ascending=False)

# Display missing values summary
print(f"📊 Dataset Shape: {df_eda.shape}")
print(f"🔍 Total Missing Values: {missing_values.sum():,}")
print(f"📈 Overall Missing Percentage: {(missing_values.sum() / (len(df_eda) * len(df_eda.columns))) * 100:.2f}%")

# Show columns with missing values
columns_with_missing = missing_df[missing_df['Missing_Count'] > 0]
if len(columns_with_missing) > 0:
    print(f"\n❌ Columns with Missing Values ({len(columns_with_missing)}):")
    print(columns_with_missing[['Column', 'Missing_Count', 'Missing_Percentage', 'Column_Type']].to_string(index=False))
    
    # Visualization of missing values
    if len(columns_with_missing) > 0:
        plt.figure(figsize=(12, 6))
        
        # Bar plot of missing percentages
        plt.subplot(1, 2, 1)
        missing_cols = columns_with_missing['Column'][:10]  # Top 10 missing
        missing_pcts = columns_with_missing['Missing_Percentage'][:10]
        
        plt.barh(range(len(missing_cols)), missing_pcts, color='lightcoral')
        plt.yticks(range(len(missing_cols)), missing_cols)
        plt.xlabel('Missing Percentage (%)')
        plt.title('Top 10 Columns with Missing Values')
        plt.gca().invert_yaxis()
        
        # Missing values heatmap (sample)
        plt.subplot(1, 2, 2)
        sample_size = min(100, len(df_eda))
        missing_matrix = df_eda.head(sample_size).isnull()
        if missing_matrix.any().any():
            plt.imshow(missing_matrix.T, cmap='Reds', aspect='auto')
            plt.title(f'Missing Values Heatmap (First {sample_size} rows)')
            plt.xlabel('Row Index')
            plt.ylabel('Columns')
        else:
            plt.text(0.5, 0.5, 'No Missing Values\nin Sample', 
                    ha='center', va='center', transform=plt.gca().transAxes,
                    fontsize=12, bbox=dict(boxstyle='round', facecolor='lightgreen'))
            plt.title('Missing Values Status')
        
        plt.tight_layout()
        plt.show()
else:
    print("\n✅ No missing values found in any column!")

# Verify CT columns have no missing values
ct_columns = [col for col in df_eda.columns if 'CT' in col]
ct_missing = df_eda[ct_columns].isnull().sum()
print(f"\n⏱️ CT Columns Missing Values Verification:")
for col in ct_columns:
    status = "✅ Clean" if ct_missing[col] == 0 else f"❌ {ct_missing[col]} missing"
    print(f"   {col}: {status}")

# Missing values by column type
print(f"\n📊 Missing Values by Column Type:")
missing_by_type = missing_df.groupby('Column_Type').agg({
    'Missing_Count': ['count', 'sum'],
    'Missing_Percentage': 'mean'
}).round(2)
missing_by_type.columns = ['Total_Columns', 'Total_Missing', 'Avg_Missing_Pct']
print(missing_by_type)

# Data completeness summary
total_cells = len(df_eda) * len(df_eda.columns)
complete_cells = total_cells - missing_values.sum()
completeness_percentage = (complete_cells / total_cells) * 100

print(f"\n📈 Data Completeness Summary:")
print(f"   • Total data points: {total_cells:,}")
print(f"   • Complete data points: {complete_cells:,}")
print(f"   • Data completeness: {completeness_percentage:.2f}%")

print("\n✅ Missing values analysis completed!")

# Missing Values Analysis and Handling
print("🔍 MISSING VALUES ANALYSIS")
print("="*50)

# Check for missing values
missing_values = df_eda.isnull().sum()
missing_percentage = (missing_values / len(df_eda)) * 100

# Create missing values summary
missing_df = pd.DataFrame({
    'Column': missing_values.index,
    'Missing_Count': missing_values.values,
    'Missing_Percentage': missing_percentage.values
}).sort_values('Missing_Count', ascending=False)

print(f"📊 Missing Values Summary:")
print(missing_df[missing_df['Missing_Count'] > 0])

# Check if there are any missing values
total_missing = missing_values.sum()
print(f"\n📈 Total Missing Values: {total_missing:,}")

if total_missing > 0:
    print(f"📊 Columns with Missing Values: {(missing_values > 0).sum()}")
    
    # Visualization of missing values
    plt.figure(figsize=(12, 6))
    
    # Plot 1: Missing values count
    plt.subplot(1, 2, 1)
    missing_cols = missing_df[missing_df['Missing_Count'] > 0]
    if len(missing_cols) > 0:
        sns.barplot(data=missing_cols, x='Missing_Count', y='Column', palette='viridis')
        plt.title('Missing Values Count by Column', fontweight='bold')
        plt.xlabel('Number of Missing Values')
    
    # Plot 2: Missing values percentage
    plt.subplot(1, 2, 2)
    if len(missing_cols) > 0:
        sns.barplot(data=missing_cols, x='Missing_Percentage', y='Column', palette='plasma')
        plt.title('Missing Values Percentage by Column', fontweight='bold')
        plt.xlabel('Percentage of Missing Values')
    
    plt.tight_layout()
    plt.show()
    
    # Handle missing values
    print(f"\n🔧 HANDLING MISSING VALUES:")
    print("="*40)
    
    # Handle numerical columns with missing values
    for col in numerical_cols:
        if df_eda[col].isnull().sum() > 0:
            median_val = df_eda[col].median()
            df_eda[col].fillna(median_val, inplace=True)
            print(f"   ✅ {col}: Filled {missing_values[col]} missing values with median ({median_val:.2f})")
    
    # Handle categorical columns with missing values
    for col in categorical_cols:
        if df_eda[col].isnull().sum() > 0:
            mode_val = df_eda[col].mode()[0] if not df_eda[col].mode().empty else 'Unknown'
            df_eda[col].fillna(mode_val, inplace=True)
            print(f"   ✅ {col}: Filled {missing_values[col]} missing values with mode ('{mode_val}')")
    
    # Verify no missing values remain
    remaining_missing = df_eda.isnull().sum().sum()
    print(f"\n✅ Missing values after handling: {remaining_missing}")
    
else:
    print("✅ Great! No missing values found in the dataset.")

print(f"\n📊 Final dataset shape: {df_eda.shape[0]:,} rows × {df_eda.shape[1]} columns")

# Important Note
print(f"\n⚠️  IMPORTANT NOTE:")
print(f"   In production, imputation should be done AFTER train/test split")
print(f"   using only training data statistics to avoid data leakage!")

# Univariate Analysis: Numerical Columns
print("📈 UNIVARIATE ANALYSIS: NUMERICAL COLUMNS")
print("="*55)

if len(numerical_cols) == 0:
    print("❌ No numerical columns found for analysis")
else:
    print(f"📊 Analyzing {len(numerical_cols)} numerical columns...")
    
    # Calculate number of rows needed for subplots
    n_cols = 3  # 3 plots per row
    n_rows = len(numerical_cols)
    
    # Create subplots for all numerical columns
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 5*n_rows))
    
    # Handle case where there's only one numerical column
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    for i, col in enumerate(numerical_cols):
        print(f"\n🔍 Analyzing: {col}")
        
        # Basic statistics
        mean_val = df_eda[col].mean()
        median_val = df_eda[col].median()
        std_val = df_eda[col].std()
        skewness = df_eda[col].skew()
        
        print(f"   Mean: {mean_val:.2f}, Median: {median_val:.2f}, Std: {std_val:.2f}, Skewness: {skewness:.2f}")
        
        # Plot 1: Histogram
        axes[i, 0].hist(df_eda[col].dropna(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[i, 0].set_title(f'{col}\nHistogram', fontweight='bold')
        axes[i, 0].set_xlabel(col)
        axes[i, 0].set_ylabel('Frequency')
        axes[i, 0].grid(True, alpha=0.3)
        
        # Plot 2: KDE (Kernel Density Estimation)
        df_eda[col].dropna().plot(kind='density', ax=axes[i, 1], color='orange', linewidth=2)
        axes[i, 1].set_title(f'{col}\nKDE Plot', fontweight='bold')
        axes[i, 1].set_xlabel(col)
        axes[i, 1].set_ylabel('Density')
        axes[i, 1].grid(True, alpha=0.3)
        
        # Plot 3: Boxplot
        axes[i, 2].boxplot(df_eda[col].dropna(), patch_artist=True, 
                          boxprops=dict(facecolor='lightgreen', alpha=0.7))
        axes[i, 2].set_title(f'{col}\nBoxplot (Outlier Detection)', fontweight='bold')
        axes[i, 2].set_ylabel(col)
        axes[i, 2].grid(True, alpha=0.3)
        
        # Outlier detection using IQR method
        Q1 = df_eda[col].quantile(0.25)
        Q3 = df_eda[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df_eda[(df_eda[col] < lower_bound) | (df_eda[col] > upper_bound)][col]
        outlier_percentage = (len(outliers) / len(df_eda)) * 100
        
        print(f"   Outliers: {len(outliers)} ({outlier_percentage:.1f}%)")
        print(f"   Range: [{df_eda[col].min():.2f}, {df_eda[col].max():.2f}]")
        
        if skewness > 1:
            print(f"   📊 Distribution: Highly right-skewed")
        elif skewness > 0.5:
            print(f"   📊 Distribution: Moderately right-skewed")
        elif skewness < -1:
            print(f"   📊 Distribution: Highly left-skewed")
        elif skewness < -0.5:
            print(f"   📊 Distribution: Moderately left-skewed")
        else:
            print(f"   📊 Distribution: Approximately normal")
    
    plt.tight_layout()
    plt.show()
    
    # Summary of numerical analysis
    print(f"\n📊 NUMERICAL ANALYSIS SUMMARY:")
    print("="*40)
    
    outlier_summary = []
    skewness_summary = []
    
    for col in numerical_cols:
        # Outlier analysis
        Q1 = df_eda[col].quantile(0.25)
        Q3 = df_eda[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = df_eda[(df_eda[col] < lower_bound) | (df_eda[col] > upper_bound)][col]
        outlier_pct = (len(outliers) / len(df_eda)) * 100
        
        if outlier_pct > 5:
            outlier_summary.append(f"{col}: {outlier_pct:.1f}%")
        
        # Skewness analysis
        skewness = df_eda[col].skew()
        if abs(skewness) > 1:
            skew_direction = "right" if skewness > 0 else "left"
            skewness_summary.append(f"{col}: {skew_direction}-skewed ({skewness:.2f})")
    
    if outlier_summary:
        print(f"🚨 Columns with >5% outliers:")
        for item in outlier_summary:
            print(f"   • {item}")
    else:
        print(f"✅ No columns have >5% outliers")
    
    if skewness_summary:
        print(f"\n📊 Highly skewed columns:")
        for item in skewness_summary:
            print(f"   • {item}")
    else:
        print(f"✅ No highly skewed columns detected")

# Univariate Analysis: Categorical Columns
print("📝 UNIVARIATE ANALYSIS: CATEGORICAL COLUMNS")
print("="*55)

if len(categorical_cols) == 0:
    print("❌ No categorical columns found for analysis")
else:
    print(f"📊 Analyzing {len(categorical_cols)} categorical columns...")
    
    # Calculate subplot dimensions
    n_cols_plot = 2  # 2 columns in subplot
    n_rows_plot = (len(categorical_cols) + 1) // 2
    
    fig, axes = plt.subplots(n_rows_plot, n_cols_plot, figsize=(16, 5*n_rows_plot))
    
    # Handle different subplot arrangements
    if n_rows_plot == 1:
        if len(categorical_cols) == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    for i, col in enumerate(categorical_cols):
        print(f"\n🔍 Analyzing: {col}")
        
        # Value counts and percentages
        value_counts = df_eda[col].value_counts()
        value_percentages = df_eda[col].value_counts(normalize=True) * 100
        
        print(f"   Unique categories: {df_eda[col].nunique()}")
        print(f"   Most common: '{value_counts.index[0]}' ({value_percentages.iloc[0]:.1f}%)")
        print(f"   Least common: '{value_counts.index[-1]}' ({value_percentages.iloc[-1]:.1f}%)")
        
        # Create count plot
        if df_eda[col].nunique() <= 20:  # If reasonable number of categories
            sns.countplot(data=df_eda, y=col, ax=axes[i], palette='Set2', order=value_counts.index)
            axes[i].set_title(f'{col}\nCategory Distribution', fontweight='bold')
            axes[i].set_xlabel('Count')
            
            # Add percentage labels
            total = len(df_eda)
            for j, (category, count) in enumerate(value_counts.items()):
                percentage = (count / total) * 100
                axes[i].text(count + total*0.01, j, f'{percentage:.1f}%', 
                           va='center', fontweight='bold', fontsize=10)
        
        else:  # Too many categories, show top 15
            top_categories = value_counts.head(15)
            sns.countplot(data=df_eda[df_eda[col].isin(top_categories.index)], 
                         y=col, ax=axes[i], palette='Set2', order=top_categories.index)
            axes[i].set_title(f'{col}\nTop 15 Categories', fontweight='bold')
            axes[i].set_xlabel('Count')
            print(f"   (Showing top 15 out of {df_eda[col].nunique()} categories)")
        
        axes[i].grid(True, alpha=0.3)
        
        # Calculate category concentration
        top_5_percentage = value_percentages.head(5).sum()
        print(f"   Top 5 categories represent: {top_5_percentage:.1f}% of data")
        
        if top_5_percentage > 80:
            print(f"   📊 Distribution: Highly concentrated")
        elif top_5_percentage > 60:
            print(f"   📊 Distribution: Moderately concentrated")
        else:
            print(f"   📊 Distribution: Well distributed")
    
    # Hide empty subplots if odd number of categorical columns
    if len(categorical_cols) % 2 == 1:
        axes[-1].set_visible(False)
    
    plt.tight_layout()
    plt.show()
    
    # Summary of categorical analysis
    print(f"\n📊 CATEGORICAL ANALYSIS SUMMARY:")
    print("="*40)
    
    high_cardinality = []
    low_cardinality = []
    imbalanced_categories = []
    
    for col in categorical_cols:
        unique_count = df_eda[col].nunique()
        value_percentages = df_eda[col].value_counts(normalize=True) * 100
        max_percentage = value_percentages.iloc[0]
        
        # Cardinality analysis
        if unique_count > 20:
            high_cardinality.append(f"{col}: {unique_count} categories")
        elif unique_count <= 5:
            low_cardinality.append(f"{col}: {unique_count} categories")
        
        # Imbalance analysis
        if max_percentage > 80:
            imbalanced_categories.append(f"{col}: {max_percentage:.1f}% in top category")
    
    if high_cardinality:
        print(f"🔢 High cardinality columns (>20 categories):")
        for item in high_cardinality:
            print(f"   • {item}")
    
    if low_cardinality:
        print(f"\n🔢 Low cardinality columns (≤5 categories):")
        for item in low_cardinality:
            print(f"   • {item}")
    
    if imbalanced_categories:
        print(f"\n⚖️  Highly imbalanced categories (>80% in one category):")
        for item in imbalanced_categories:
            print(f"   • {item}")
    
    if not high_cardinality and not low_cardinality and not imbalanced_categories:
        print(f"✅ All categorical columns have reasonable cardinality and distribution")

# Bivariate Analysis: Numerical vs Target
print("📈🎯 BIVARIATE ANALYSIS: NUMERICAL vs TARGET")
print("="*55)

if len(numerical_cols) == 0:
    print("❌ No numerical columns found for bivariate analysis")
else:
    print(f"📊 Analyzing {len(numerical_cols)} numerical columns vs target '{target_variable}'...")
    
    # Create subplots - 2 plots per numerical column (boxplot and KDE)
    n_cols_plot = 2  # boxplot and KDE plot
    n_rows = len(numerical_cols)
    
    fig, axes = plt.subplots(n_rows, n_cols_plot, figsize=(16, 5*n_rows))
    
    # Handle case where there's only one numerical column
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    significant_features = []
    
    for i, col in enumerate(numerical_cols):
        print(f"\n🔍 Analyzing: {col} vs {target_variable}")
        
        # Separate data by target groups
        group_0 = df_eda[df_eda[target_variable] == 0][col]  # On-time
        group_1 = df_eda[df_eda[target_variable] == 1][col]  # Late
        
        # Calculate statistics for each group
        mean_0 = group_0.mean()
        mean_1 = group_1.mean()
        median_0 = group_0.median()
        median_1 = group_1.median()
        
        print(f"   On-time (0): Mean={mean_0:.2f}, Median={median_0:.2f}")
        print(f"   Late (1): Mean={mean_1:.2f}, Median={median_1:.2f}")
        print(f"   Difference in means: {abs(mean_1 - mean_0):.2f}")
        
        # Plot 1: Boxplot by target
        sns.boxplot(data=df_eda, x=target_variable, y=col, ax=axes[i, 0], 
                   palette=['lightgreen', 'lightcoral'])
        axes[i, 0].set_title(f'{col} by Billing Status\n(Boxplot)', fontweight='bold')
        axes[i, 0].set_xlabel('Billing Status (0=On-time, 1=Late)')
        axes[i, 0].grid(True, alpha=0.3)
        
        # Plot 2: KDE by target
        group_0.plot(kind='density', ax=axes[i, 1], alpha=0.7, color='green', 
                    label='On-time (0)', linewidth=2)
        group_1.plot(kind='density', ax=axes[i, 1], alpha=0.7, color='red', 
                    label='Late (1)', linewidth=2)
        axes[i, 1].set_title(f'{col} Distribution by Billing Status\n(KDE)', fontweight='bold')
        axes[i, 1].set_xlabel(col)
        axes[i, 1].set_ylabel('Density')
        axes[i, 1].legend()
        axes[i, 1].grid(True, alpha=0.3)
        
        # Statistical test (Mann-Whitney U test for non-parametric comparison)
        try:
            from scipy.stats import mannwhitneyu
            statistic, p_value = mannwhitneyu(group_0.dropna(), group_1.dropna(), alternative='two-sided')
            
            print(f"   Mann-Whitney U test p-value: {p_value:.4f}")
            
            if p_value < 0.05:
                significance = "🟢 SIGNIFICANT"
                significant_features.append(col)
            elif p_value < 0.1:
                significance = "🟡 MARGINALLY SIGNIFICANT"
            else:
                significance = "🔴 NOT SIGNIFICANT"
            
            print(f"   Statistical significance: {significance}")
            
            # Effect size (difference in medians relative to pooled standard deviation)
            pooled_std = df_eda[col].std()
            effect_size = abs(median_1 - median_0) / pooled_std if pooled_std > 0 else 0
            print(f"   Effect size: {effect_size:.3f}")
            
            if effect_size > 0.5:
                print(f"   📊 Effect: Large effect")
            elif effect_size > 0.3:
                print(f"   📊 Effect: Medium effect")
            elif effect_size > 0.1:
                print(f"   📊 Effect: Small effect")
            else:
                print(f"   📊 Effect: Negligible effect")
                
        except Exception as e:
            print(f"   ⚠️ Statistical test failed: {e}")
    
    plt.tight_layout()
    plt.show()
    
    # Summary of bivariate analysis
    print(f"\n📊 NUMERICAL vs TARGET ANALYSIS SUMMARY:")
    print("="*50)
    
    if significant_features:
        print(f"🟢 Statistically significant features ({len(significant_features)}):")
        for feature in significant_features:
            print(f"   • {feature}")
        print(f"\n💡 These features show significant differences between on-time and late billing!")
        print(f"   Consider these as important predictors for your logistic regression model.")
    else:
        print(f"🔴 No statistically significant features found.")
        print(f"   This might indicate:")
        print(f"   • Need for feature engineering")
        print(f"   • Non-linear relationships")
        print(f"   • Interaction effects between features")
    
    print(f"\n📈 Total numerical features analyzed: {len(numerical_cols)}")
    print(f"🎯 Features with predictive potential: {len(significant_features)}")

# Bivariate Analysis: Categorical vs Target
print("📝🎯 BIVARIATE ANALYSIS: CATEGORICAL vs TARGET")
print("="*55)

if len(categorical_cols) == 0:
    print("❌ No categorical columns found for bivariate analysis")
else:
    print(f"📊 Analyzing {len(categorical_cols)} categorical columns vs target '{target_variable}'...")
    
    # Calculate subplot dimensions
    n_cols_plot = 2  # 2 columns in subplot
    n_rows_plot = (len(categorical_cols) + 1) // 2
    
    fig, axes = plt.subplots(n_rows_plot, n_cols_plot, figsize=(18, 6*n_rows_plot))
    
    # Handle different subplot arrangements
    if n_rows_plot == 1:
        if len(categorical_cols) == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    significant_categorical_features = []
    
    for i, col in enumerate(categorical_cols):
        print(f"\n🔍 Analyzing: {col} vs {target_variable}")
        
        # Create crosstab for chi-square test
        crosstab = pd.crosstab(df_eda[col], df_eda[target_variable])
        
        print(f"   Categories: {df_eda[col].nunique()}")
        
        # Calculate percentages within each category
        crosstab_pct = pd.crosstab(df_eda[col], df_eda[target_variable], normalize='index') * 100
        
        # Find categories with highest late billing percentage
        late_pct_by_category = crosstab_pct[1].sort_values(ascending=False)
        print(f"   Highest late billing: '{late_pct_by_category.index[0]}' ({late_pct_by_category.iloc[0]:.1f}%)")
        print(f"   Lowest late billing: '{late_pct_by_category.index[-1]}' ({late_pct_by_category.iloc[-1]:.1f}%)")
        
        # Create grouped count plot
        if df_eda[col].nunique() <= 15:  # If reasonable number of categories
            sns.countplot(data=df_eda, x=col, hue=target_variable, ax=axes[i], 
                         palette=['lightgreen', 'lightcoral'])
            axes[i].set_title(f'{col} by Billing Status\n(Count Plot)', fontweight='bold')
            axes[i].set_xlabel(col)
            axes[i].set_ylabel('Count')
            
            # Rotate x-axis labels if needed
            if df_eda[col].nunique() > 5:
                axes[i].tick_params(axis='x', rotation=45)
        
        else:  # Too many categories, show top 10
            top_categories = df_eda[col].value_counts().head(10).index
            df_subset = df_eda[df_eda[col].isin(top_categories)]
            sns.countplot(data=df_subset, x=col, hue=target_variable, ax=axes[i], 
                         palette=['lightgreen', 'lightcoral'])
            axes[i].set_title(f'{col} by Billing Status\n(Top 10 Categories)', fontweight='bold')
            axes[i].set_xlabel(col)
            axes[i].set_ylabel('Count')
            axes[i].tick_params(axis='x', rotation=45)
            print(f"   (Showing top 10 out of {df_eda[col].nunique()} categories)")
        
        axes[i].legend(title='Billing Status', labels=['On-time', 'Late'])
        axes[i].grid(True, alpha=0.3)
        
        # Chi-square test for independence
        try:
            from scipy.stats import chi2_contingency
            
            # Perform chi-square test
            chi2, p_value, dof, expected = chi2_contingency(crosstab)
            
            print(f"   Chi-square statistic: {chi2:.4f}")
            print(f"   p-value: {p_value:.4f}")
            print(f"   Degrees of freedom: {dof}")
            
            if p_value < 0.05:
                significance = "🟢 SIGNIFICANT"
                significant_categorical_features.append(col)
            elif p_value < 0.1:
                significance = "🟡 MARGINALLY SIGNIFICANT"
            else:
                significance = "🔴 NOT SIGNIFICANT"
            
            print(f"   Statistical significance: {significance}")
            
            # Cramér's V (effect size for categorical associations)
            n = crosstab.sum().sum()
            cramers_v = np.sqrt(chi2 / (n * (min(crosstab.shape) - 1)))
            print(f"   Cramér's V (effect size): {cramers_v:.3f}")
            
            if cramers_v > 0.5:
                print(f"   📊 Association: Strong")
            elif cramers_v > 0.3:
                print(f"   📊 Association: Moderate")
            elif cramers_v > 0.1:
                print(f"   📊 Association: Weak")
            else:
                print(f"   📊 Association: Very weak")
                
        except Exception as e:
            print(f"   ⚠️ Chi-square test failed: {e}")
        
        # Show percentage table for top categories
        print(f"   Late billing % by category (top 5):")
        for category, pct in late_pct_by_category.head(5).items():
            count = crosstab.loc[category].sum()
            print(f"     '{category}': {pct:.1f}% (n={count})")
    
    # Hide empty subplots if odd number of categorical columns
    if len(categorical_cols) % 2 == 1:
        axes[-1].set_visible(False)
    
    plt.tight_layout()
    plt.show()
    
    # Summary of categorical bivariate analysis
    print(f"\n📊 CATEGORICAL vs TARGET ANALYSIS SUMMARY:")
    print("="*50)
    
    if significant_categorical_features:
        print(f"🟢 Statistically significant features ({len(significant_categorical_features)}):")
        for feature in significant_categorical_features:
            print(f"   • {feature}")
        print(f"\n💡 These categorical features show significant association with billing status!")
        print(f"   Consider encoding these for your logistic regression model.")
    else:
        print(f"🔴 No statistically significant categorical features found.")
    
    print(f"\n📈 Total categorical features analyzed: {len(categorical_cols)}")
    print(f"🎯 Features with predictive potential: {len(significant_categorical_features)}")
    
    # Recommendations for categorical encoding
    if significant_categorical_features:
        print(f"\n🔧 ENCODING RECOMMENDATIONS:")
        for feature in significant_categorical_features:
            unique_count = df_eda[feature].nunique()
            if unique_count == 2:
                print(f"   • {feature}: Use Label Encoding (binary)")
            elif unique_count <= 5:
                print(f"   • {feature}: Use One-Hot Encoding")
            else:
                print(f"   • {feature}: Consider Target Encoding or Feature Selection")

# Multivariate Analysis: Correlation Heatmap
print("🌡️ CORRELATION ANALYSIS: NUMERICAL FEATURES")
print("="*55)

if len(numerical_cols) < 2:
    print("❌ Need at least 2 numerical columns for correlation analysis")
else:
    print(f"📊 Analyzing correlations between {len(numerical_cols)} numerical features...")
    
    # Calculate correlation matrix
    correlation_matrix = df_eda[numerical_cols].corr()
    
    # Create correlation heatmap
    plt.figure(figsize=(12, 10))
    
    # Create mask for upper triangle (to show only lower triangle)
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    
    # Create heatmap
    sns.heatmap(correlation_matrix, 
                mask=mask,
                annot=True, 
                cmap='RdBu_r', 
                center=0,
                square=True,
                fmt='.3f',
                cbar_kws={'shrink': 0.8},
                linewidths=0.5)
    
    plt.title('Correlation Heatmap of Numerical Features', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Features', fontsize=12)
    plt.ylabel('Features', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    
    # Find high correlations
    print(f"\n🔍 HIGH CORRELATION ANALYSIS:")
    print("="*40)
    
    # Get correlation pairs (excluding diagonal and duplicates)
    high_corr_pairs = []
    correlation_threshold = 0.7
    
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_value = correlation_matrix.iloc[i, j]
            if abs(corr_value) >= correlation_threshold:
                feature1 = correlation_matrix.columns[i]
                feature2 = correlation_matrix.columns[j]
                high_corr_pairs.append((feature1, feature2, corr_value))
    
    if high_corr_pairs:
        print(f"🚨 High correlations found (|r| ≥ {correlation_threshold}):")
        for feature1, feature2, corr in high_corr_pairs:
            correlation_strength = "Strong Positive" if corr > 0 else "Strong Negative"
            print(f"   • {feature1} ↔ {feature2}: {corr:.3f} ({correlation_strength})")
        
        print(f"\n⚠️  MULTICOLLINEARITY WARNING:")
        print(f"   High correlations may cause multicollinearity in logistic regression.")
        print(f"   Consider:")
        print(f"   • Removing one of the highly correlated features")
        print(f"   • Using Principal Component Analysis (PCA)")
        print(f"   • Using regularization (Ridge/Lasso)")
    else:
        print(f"✅ No high correlations found (|r| < {correlation_threshold})")
        print(f"   Multicollinearity is unlikely to be an issue.")
    
    # Correlation with target variable
    if target_variable in df_eda.columns:
        print(f"\n🎯 CORRELATION WITH TARGET VARIABLE:")
        print("="*45)
        
        # Calculate correlations with target
        target_correlations = df_eda[numerical_cols + [target_variable]].corr()[target_variable].drop(target_variable)
        target_correlations_sorted = target_correlations.abs().sort_values(ascending=False)
        
        print(f"Correlations with '{target_variable}' (sorted by absolute value):")
        for feature, corr in target_correlations_sorted.items():
            original_corr = target_correlations[feature]
            if abs(corr) > 0.3:
                strength = "🟢 Strong"
            elif abs(corr) > 0.1:
                strength = "🟡 Moderate"
            else:
                strength = "🔴 Weak"
            
            direction = "Positive" if original_corr > 0 else "Negative"
            print(f"   • {feature}: {original_corr:.3f} ({direction}) {strength}")
        
        # Find the most correlated features with target
        top_correlated = target_correlations_sorted.head(3)
        if len(top_correlated) > 0:
            print(f"\n🏆 Top 3 features correlated with target:")
            for i, (feature, abs_corr) in enumerate(top_correlated.items(), 1):
                original_corr = target_correlations[feature]
                print(f"   {i}. {feature}: {original_corr:.3f}")
    
    # Summary statistics for correlations
    print(f"\n📊 CORRELATION SUMMARY:")
    print("="*30)
    
    # Get all correlation values (excluding diagonal)
    all_correlations = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            all_correlations.append(abs(correlation_matrix.iloc[i, j]))
    
    if all_correlations:
        print(f"📈 Average absolute correlation: {np.mean(all_correlations):.3f}")
        print(f"📈 Maximum absolute correlation: {np.max(all_correlations):.3f}")
        print(f"📈 Minimum absolute correlation: {np.min(all_correlations):.3f}")
        
        # Count correlations by strength
        strong_count = sum(1 for corr in all_correlations if corr >= 0.7)
        moderate_count = sum(1 for corr in all_correlations if 0.3 <= corr < 0.7)
        weak_count = sum(1 for corr in all_correlations if corr < 0.3)
        
        print(f"\n📊 Correlation strength distribution:")
        print(f"   🟢 Strong (≥0.7): {strong_count}")
        print(f"   🟡 Moderate (0.3-0.7): {moderate_count}")
        print(f"   🔴 Weak (<0.3): {weak_count}")
    
    print(f"\n✅ Correlation analysis completed!")