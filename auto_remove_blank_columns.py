#!/usr/bin/env python3
"""
Auto Remove Blank Columns Script
================================
Automatically remove columns with blank/missing values from CT cycle time data.
"""

import pandas as pd
import os
from datetime import datetime

def auto_remove_blank_columns():
    """Automatically remove blank columns from the dataset."""
    
    INPUT_FILE = "20250613160303_CELONIS_EXPORT.csv"
    
    print("="*60)
    print("     AUTO REMOVE BLANK COLUMNS SCRIPT")
    print("="*60)
    
    # Check if file exists
    if not os.path.exists(INPUT_FILE):
        print(f"❌ File not found: {INPUT_FILE}")
        return
    
    # Load data
    print(f"📂 Loading: {INPUT_FILE}")
    df = pd.read_csv(INPUT_FILE)
    df.columns = df.columns.str.strip()
    
    print(f"📊 Original: {df.shape[0]:,} rows × {df.shape[1]} columns")
    
    # Find columns with missing values
    missing_counts = df.isnull().sum()
    columns_with_missing = missing_counts[missing_counts > 0].index.tolist()
    columns_complete = missing_counts[missing_counts == 0].index.tolist()
    
    print(f"\n🔍 Analysis:")
    print(f"   Columns with missing data: {len(columns_with_missing)}")
    print(f"   Columns with complete data: {len(columns_complete)}")
    
    # Show columns being removed
    if columns_with_missing:
        print(f"\n🗑️  Removing {len(columns_with_missing)} columns with missing data:")
        for col in columns_with_missing:
            missing_count = missing_counts[col]
            missing_pct = (missing_count / len(df)) * 100
            print(f"   • {col}: {missing_count:,} missing ({missing_pct:.1f}%)")
    
    # Create clean dataset with only complete columns
    df_clean = df[columns_complete].copy()
    
    # Generate output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"20250613160303_CELONIS_EXPORT_no_blanks_{timestamp}.csv"
    
    # Save clean data
    df_clean.to_csv(output_file, index=False)
    file_size = os.path.getsize(output_file) / 1024**2
    
    print(f"\n📈 Results:")
    print(f"   Clean dataset: {df_clean.shape[0]:,} rows × {df_clean.shape[1]} columns")
    print(f"   Removed: {len(columns_with_missing)} columns")
    print(f"   Saved to: {output_file}")
    print(f"   File size: {file_size:.2f} MB")
    
    print(f"\n📋 Remaining columns:")
    for i, col in enumerate(df_clean.columns, 1):
        print(f"   {i:2d}. {col}")
    
    print(f"\n✅ Completed! No missing values in the clean dataset.")
    print("="*60)

if __name__ == "__main__":
    auto_remove_blank_columns()
