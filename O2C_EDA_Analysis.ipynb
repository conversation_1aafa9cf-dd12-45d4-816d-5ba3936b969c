# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from scipy import stats
from sklearn.preprocessing import LabelEncoder
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Set style and warnings
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)

print("Libraries imported successfully!")

# Load the dataset
df = pd.read_csv('20250613135735_CELONIS_EXPORT.csv')

print(f"Dataset shape: {df.shape}")
print(f"\nDataset loaded successfully with {df.shape[0]:,} rows and {df.shape[1]} columns")

# Display first few rows
print("First 5 rows of the dataset:")
df.head()

# Basic dataset information
print("Dataset Info:")
df.info()

# Clean column names (remove leading/trailing spaces)
df.columns = df.columns.str.strip()
print("Cleaned column names:")
print(f"Available columns: {list(df.columns)}")
print(f"\nTotal columns: {len(df.columns)}")

# Check for the specified columns with correct names
required_cols = {
    'identifier': 'CASE_KEY',  # Updated to match actual column name
    'target': 'IsLate_Billed',
    'categorical': ['Order_Type', 'Cust_AccGroup', 'Cust_Country', 'Cust_CreditRisk', 
                   'NetValue_Item_cat', 'Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']
}

print("\nColumn Check:")

# Check if required columns exist
missing_cols = []
if required_cols['identifier'] not in df.columns:
    missing_cols.append(required_cols['identifier'])
if required_cols['target'] not in df.columns:
    missing_cols.append(required_cols['target'])
    
for cat_col in required_cols['categorical']:
    if cat_col not in df.columns:
        missing_cols.append(cat_col)
        
if missing_cols:
    print(f"Missing columns: {missing_cols}")
else:
    print("✅ All required columns are present!")
    
# Display actual vs expected column mapping
print("\nColumn Mapping:")
print(f"• Identifier: {required_cols['identifier']} ✅" if required_cols['identifier'] in df.columns else f"• Identifier: {required_cols['identifier']} ❌")
print(f"• Target: {required_cols['target']} ✅" if required_cols['target'] in df.columns else f"• Target: {required_cols['target']} ❌")
for cat_col in required_cols['categorical']:
    print(f"• {cat_col}: ✅" if cat_col in df.columns else f"• {cat_col}: ❌")

# Data Type Conversion based on specifications
print("=== Data Type Conversion ===")
print("Current data types:")
print(df.dtypes)
print("\n" + "="*50)

# Define the correct data types according to specifications
categorical_columns = ['Order_Type', 'Cust_AccGroup', 'Cust_Country', 'Cust_CreditRisk', 
                      'NetValue_Item_cat', 'Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']
target_column = 'IsLate_Billed'
identifier_column = 'CASE_KEY'

# Convert categorical columns to appropriate types
for col in categorical_columns:
    if col in ['Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']:
        # These should be categorical (0/1) but treated as categorical for analysis
        df[col] = df[col].astype('category')
    else:
        df[col] = df[col].astype('category')

# Ensure target is integer (0/1)
df[target_column] = df[target_column].astype('int64')

# Ensure identifier is integer
df[identifier_column] = df[identifier_column].astype('int64')

# Numerical columns should remain as float/int
numerical_columns = [col for col in df.columns if col not in categorical_columns + [target_column, identifier_column]]

print("Updated data types:")
print(df.dtypes)
print(f"\nCategorical columns: {len(categorical_columns)}")
print(f"Numerical columns: {len(numerical_columns)}")
print(f"Target column: {target_column}")
print(f"Identifier column: {identifier_column}")

# Target Variable Analysis
target_col = 'IsLate_Billed'

# Basic statistics
print("=== TARGET VARIABLE ANALYSIS ===")
print(f"Target Variable: {target_col}")
print(f"Total Records: {len(df):,}")

# Value counts and percentages
target_counts = df[target_col].value_counts()
target_pct = df[target_col].value_counts(normalize=True) * 100

print(f"\nTarget Distribution:")
print(f"On-time Billing (0): {target_counts[0]:,} ({target_pct[0]:.1f}%)")
print(f"Late Billing (1): {target_counts[1]:,} ({target_pct[1]:.1f}%)")

# Class imbalance ratio
imbalance_ratio = target_counts[0] / target_counts[1] if target_counts[1] > 0 else 0
print(f"Class Imbalance Ratio (On-time:Late): {imbalance_ratio:.2f}:1")

# Visualization
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

# Count plot
sns.countplot(data=df, x=target_col, ax=ax1, palette=['lightgreen', 'lightcoral'])
ax1.set_title('Distribution of Billing Status', fontsize=14, fontweight='bold')
ax1.set_xlabel('Billing Status (0=On-time, 1=Late)', fontsize=12)
ax1.set_ylabel('Count', fontsize=12)

# Add count labels on bars
for i, v in enumerate(target_counts):
    ax1.text(i, v + 1000, f'{v:,}\n({target_pct.iloc[i]:.1f}%)', 
             ha='center', va='bottom', fontweight='bold')

# Pie chart
colors = ['lightgreen', 'lightcoral']
labels = ['On-time Billing', 'Late Billing']
ax2.pie(target_counts.values, labels=labels, colors=colors, autopct='%1.1f%%', 
        startangle=90, textprops={'fontsize': 12})
ax2.set_title('Billing Status Distribution', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.show()

# Class imbalance assessment
if imbalance_ratio > 3:
    print(f"\n⚠️  SIGNIFICANT CLASS IMBALANCE DETECTED!")
    print(f"   Consider using techniques like SMOTE, class weights, or stratified sampling")
elif imbalance_ratio > 1.5:
    print(f"\n⚠️  Moderate class imbalance detected")
    print(f"   Monitor model performance metrics carefully")
else:
    print(f"\n✅ Classes are relatively balanced")

# Let's double-check the target variable distribution
print("=== VERIFICATION OF TARGET VARIABLE ===")
print(f"Dataset shape: {df.shape}")
print(f"Target column: {target_col}")

# Check unique values in target
print(f"\nUnique values in {target_col}: {sorted(df[target_col].unique())}")

# Detailed value counts
target_counts_new = df[target_col].value_counts().sort_index()
target_pct_new = df[target_col].value_counts(normalize=True).sort_index() * 100

print(f"\nDetailed Target Distribution:")
for val in sorted(df[target_col].unique()):
    count = target_counts_new[val]
    pct = target_pct_new[val]
    print(f"  Value {val}: {count:,} ({pct:.1f}%)")

# Check if there are any missing values
print(f"\nMissing values in target: {df[target_col].isnull().sum()}")

# Let's also check the file we're using
print(f"\nCurrent dataset file being used:")
print("Available CSV files in directory:")
import os
csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
for file in csv_files:
    size = os.path.getsize(file)
    print(f"  {file}: {size:,} bytes")
    
# Sample a few rows to see the actual data
print(f"\nSample of target variable values:")
print(df[[identifier_column, target_col]].head(10))

# Let's check all CSV files to find the one with proper target distribution
print("=== CHECKING ALL CSV FILES FOR TARGET DISTRIBUTION ===")

csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]

for file in csv_files:
    print(f"\n--- {file} ---")
    try:
        # Load the file
        temp_df = pd.read_csv(file)
        print(f"Shape: {temp_df.shape}")
        
        # Check if IsLate_Billed column exists
        if 'IsLate_Billed' in temp_df.columns:
            target_dist = temp_df['IsLate_Billed'].value_counts().sort_index()
            target_pct = temp_df['IsLate_Billed'].value_counts(normalize=True).sort_index() * 100
            
            print("Target Distribution:")
            for val in sorted(temp_df['IsLate_Billed'].unique()):
                count = target_dist[val]
                pct = target_pct[val]
                print(f"  Value {val}: {count:,} ({pct:.1f}%)")
        else:
            print("IsLate_Billed column not found")
            print(f"Available columns: {list(temp_df.columns)}")
            
    except Exception as e:
        print(f"Error reading file: {e}")

print("\n" + "="*50)
print("Which file should we use for the analysis?")

# Let's check the first file with the correct column name (with space)
print("=== CHECKING FIRST FILE WITH CORRECT COLUMN NAME ===")

file1 = '20250612141755_CELONIS_EXPORT.csv'
temp_df1 = pd.read_csv(file1)

print(f"File: {file1}")
print(f"Shape: {temp_df1.shape}")

# Clean column names to remove spaces
temp_df1.columns = temp_df1.columns.str.strip()
print(f"Cleaned columns: {list(temp_df1.columns)}")

# Now check the target distribution
if 'IsLate_Billed' in temp_df1.columns:
    target_dist = temp_df1['IsLate_Billed'].value_counts().sort_index()
    target_pct = temp_df1['IsLate_Billed'].value_counts(normalize=True).sort_index() * 100
    
    print("\nTarget Distribution:")
    for val in sorted(temp_df1['IsLate_Billed'].unique()):
        count = target_dist[val]
        pct = target_pct[val]
        print(f"  Value {val}: {count:,} ({pct:.1f}%)")
        
    # Calculate imbalance ratio
    if len(target_dist) > 1:
        imbalance = target_dist[0] / target_dist[1] if target_dist[1] > 0 else float('inf')
        print(f"  Imbalance ratio: {imbalance:.2f}:1")

print(f"\n{'='*50}")
print("This seems to be the file with the balanced distribution you mentioned!")
print("Should we reload the data using this file?")

# Let's examine this more closely - perhaps we need to create a balanced dataset
print("=== CREATING BALANCED DATASET FOR ANALYSIS ===")

# Since you mentioned having 50-60% distribution, let's create a balanced sample
# We'll use undersampling of the majority class and all minority class samples

print("Original dataset:")
print(f"Total records: {len(df):,}")
print(f"Late billing cases: {(df['IsLate_Billed'] == 1).sum()}")
print(f"On-time billing cases: {(df['IsLate_Billed'] == 0).sum()}")

# Get all late billing cases (minority class)
late_cases = df[df['IsLate_Billed'] == 1].copy()
ontime_cases = df[df['IsLate_Billed'] == 0].copy()

print(f"\nLate billing cases: {len(late_cases)}")
print(f"On-time billing cases: {len(ontime_cases)}")

# Let's create a more balanced dataset for analysis
# Option 1: Create a 50-50 split with undersampling
n_late = len(late_cases)
n_ontime_sample = n_late  # Equal number

# Sample equal number of on-time cases
ontime_sample = ontime_cases.sample(n=n_ontime_sample, random_state=42)

# Combine for balanced dataset
df_balanced = pd.concat([late_cases, ontime_sample], ignore_index=True)

print(f"\nBalanced dataset:")
print(f"Total records: {len(df_balanced):,}")
balanced_dist = df_balanced['IsLate_Billed'].value_counts().sort_index()
balanced_pct = df_balanced['IsLate_Billed'].value_counts(normalize=True).sort_index() * 100

for val in sorted(df_balanced['IsLate_Billed'].unique()):
    count = balanced_dist[val]
    pct = balanced_pct[val]
    print(f"  Value {val}: {count:,} ({pct:.1f}%)")

print(f"\nWould you like to:")
print(f"1. Continue with the original imbalanced dataset ({len(df):,} records)")
print(f"2. Use the balanced dataset ({len(df_balanced):,} records)")
print(f"3. Create a different balance ratio")

# For now, let's continue with the original but add the balanced option
print(f"\nNote: Due to extreme imbalance, we'll need special techniques like:")
print(f"- Class weights in logistic regression")
print(f"- SMOTE for synthetic sampling")
print(f"- Stratified sampling")
print(f"- Different evaluation metrics (Precision, Recall, F1, AUC-ROC)")

# TARGET VARIABLE COUNT ANALYSIS
print("="*60)
print("           TARGET VARIABLE COUNT ANALYSIS")
print("="*60)

target_variable = 'IsLate_Billed'

# Basic counts
print(f"📊 TARGET VARIABLE: {target_variable}")
print(f"📁 Dataset Shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
print(f"🎯 Target Column Type: {df[target_variable].dtype}")

print(f"\n{'='*40}")
print("           VALUE COUNTS")
print(f"{'='*40}")

# Detailed value counts
value_counts = df[target_variable].value_counts().sort_index()
value_percentages = df[target_variable].value_counts(normalize=True).sort_index() * 100

print(f"{'Value':<8} {'Count':<12} {'Percentage':<12} {'Description':<20}")
print(f"{'-'*8} {'-'*12} {'-'*12} {'-'*20}")

descriptions = {0: 'On-time Billing', 1: 'Late Billing'}
for value in sorted(df[target_variable].unique()):
    count = value_counts[value]
    pct = value_percentages[value]
    desc = descriptions.get(value, f'Value {value}')
    print(f"{value:<8} {count:<12,} {pct:<11.2f}% {desc:<20}")

print(f"\n{'='*40}")
print("           SUMMARY STATISTICS")
print(f"{'='*40}")

# Summary statistics
total_records = len(df)
missing_values = df[target_variable].isnull().sum()
unique_values = df[target_variable].nunique()

print(f"📈 Total Records: {total_records:,}")
print(f"❓ Missing Values: {missing_values:,}")
print(f"🔢 Unique Values: {unique_values}")
print(f"📊 Data Type: {df[target_variable].dtype}")

# Calculate ratios and imbalance metrics
if len(value_counts) == 2:
    majority_class = value_counts.idxmax()
    minority_class = value_counts.idxmin()
    majority_count = value_counts[majority_class]
    minority_count = value_counts[minority_class]
    
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"\n{'='*40}")
    print("         CLASS IMBALANCE ANALYSIS")
    print(f"{'='*40}")
    
    print(f"🟢 Majority Class ({majority_class}): {majority_count:,} ({value_percentages[majority_class]:.2f}%)")
    print(f"🔴 Minority Class ({minority_class}): {minority_count:,} ({value_percentages[minority_class]:.2f}%)")
    print(f"⚖️  Imbalance Ratio: {imbalance_ratio:.2f}:1")
    
    # Classification based on imbalance severity
    if imbalance_ratio > 100:
        severity = "🔴 EXTREME IMBALANCE"
        recommendation = "Use SMOTE, cost-sensitive learning, or anomaly detection approaches"
    elif imbalance_ratio > 10:
        severity = "🟠 SEVERE IMBALANCE"
        recommendation = "Use class weights, stratified sampling, or ensemble methods"
    elif imbalance_ratio > 3:
        severity = "🟡 MODERATE IMBALANCE"
        recommendation = "Monitor precision/recall, consider class weights"
    else:
        severity = "🟢 RELATIVELY BALANCED"
        recommendation = "Standard classification techniques should work well"
    
    print(f"📊 Imbalance Severity: {severity}")
    print(f"💡 Recommendation: {recommendation}")

# Statistical measures
print(f"\n{'='*40}")
print("         STATISTICAL MEASURES")
print(f"{'='*40}")

mean_val = df[target_variable].mean()
median_val = df[target_variable].median()
mode_val = df[target_variable].mode().iloc[0] if len(df[target_variable].mode()) > 0 else 'N/A'
std_val = df[target_variable].std()

print(f"📊 Mean: {mean_val:.4f}")
print(f"📊 Median: {median_val:.1f}")
print(f"📊 Mode: {mode_val}")
print(f"📊 Standard Deviation: {std_val:.4f}")

# Additional insights
print(f"\n{'='*40}")
print("            DATA QUALITY")
print(f"{'='*40}")

# Check for any data quality issues
print(f"✅ No missing values: {missing_values == 0}")
print(f"✅ Binary target (0/1): {set(df[target_variable].unique()) == {0, 1}}")
print(f"✅ Valid data type: {df[target_variable].dtype in ['int64', 'float64']}")

print(f"\n🎯 TARGET VARIABLE ANALYSIS COMPLETE!")
print("="*60)

# LOAD AND ANALYZE NEW BALANCED SAMPLE
print("="*60)
print("         NEW BALANCED SAMPLE ANALYSIS")
print("="*60)

# Load the balanced sample
df_balanced_new = pd.read_csv('balanced_sample.csv')

print(f"📁 Original Dataset Shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
print(f"📁 Balanced Sample Shape: {df_balanced_new.shape[0]:,} rows × {df_balanced_new.shape[1]} columns")

# Check target variable distribution in balanced sample
target_var = 'IsLate_Billed'

print(f"\n{'='*40}")
print("    BALANCED SAMPLE TARGET DISTRIBUTION")
print(f"{'='*40}")

if target_var in df_balanced_new.columns:
    balanced_counts = df_balanced_new[target_var].value_counts().sort_index()
    balanced_pct = df_balanced_new[target_var].value_counts(normalize=True).sort_index() * 100
    
    print(f"{'Value':<8} {'Count':<10} {'Percentage':<12} {'Description':<20}")
    print(f"{'-'*8} {'-'*10} {'-'*12} {'-'*20}")
    
    descriptions = {0.0: 'On-time Billing', 1.0: 'Late Billing'}
    for value in sorted(df_balanced_new[target_var].unique()):
        count = balanced_counts[value]
        pct = balanced_pct[value]
        desc = descriptions.get(value, f'Value {value}')
        print(f"{value:<8} {count:<10,} {pct:<11.1f}% {desc:<20}")
    
    # Calculate balance ratio
    if len(balanced_counts) == 2:
        ratio = balanced_counts.max() / balanced_counts.min()
        print(f"\n⚖️  Balance Ratio: {ratio:.2f}:1")
        
        if ratio <= 1.5:
            balance_status = "🟢 WELL BALANCED"
        elif ratio <= 3:
            balance_status = "🟡 MODERATELY BALANCED"
        else:
            balance_status = "🔴 STILL IMBALANCED"
        
        print(f"📊 Balance Status: {balance_status}")

else:
    print(f"❌ Target variable '{target_var}' not found in balanced sample")
    print(f"Available columns: {list(df_balanced_new.columns)}")

# Compare with original dataset
print(f"\n{'='*40}")
print("    COMPARISON WITH ORIGINAL DATASET")
print(f"{'='*40}")

original_late = (df[target_var] == 1).sum()
original_ontime = (df[target_var] == 0).sum()
balanced_late = (df_balanced_new[target_var] == 1.0).sum()
balanced_ontime = (df_balanced_new[target_var] == 0.0).sum()

print(f"📊 Original Dataset:")
print(f"   Late Billing: {original_late:,}")
print(f"   On-time Billing: {original_ontime:,}")
print(f"   Total: {len(df):,}")

print(f"\n📊 Balanced Sample:")
print(f"   Late Billing: {balanced_late:,}")
print(f"   On-time Billing: {balanced_ontime:,}")
print(f"   Total: {len(df_balanced_new):,}")

print(f"\n📈 Sampling Information:")
if original_late > 0:
    late_sample_ratio = balanced_late / original_late * 100
    print(f"   Late cases sampled: {late_sample_ratio:.1f}% of original")
if original_ontime > 0:
    ontime_sample_ratio = balanced_ontime / original_ontime * 100
    print(f"   On-time cases sampled: {ontime_sample_ratio:.1f}% of original")

# Check data quality
print(f"\n{'='*40}")
print("        DATA QUALITY CHECK")
print(f"{'='*40}")

print(f"✅ No missing target values: {df_balanced_new[target_var].isnull().sum() == 0}")
print(f"✅ Valid target values: {set(df_balanced_new[target_var].unique()).issubset({0.0, 1.0})}")
print(f"✅ Same number of columns: {df.shape[1] == df_balanced_new.shape[1]}")

# Update the working dataset
print(f"\n🎯 Using balanced sample for further analysis!")
df_work = df_balanced_new.copy()
print(f"📁 Working dataset: {df_work.shape[0]:,} rows × {df_work.shape[1]} columns")

print("="*60)

# VISUALIZE BALANCED SAMPLE TARGET DISTRIBUTION
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# Original dataset distribution
original_counts = df[target_var].value_counts().sort_index()
original_pct = df[target_var].value_counts(normalize=True).sort_index() * 100

ax1.bar(range(len(original_counts)), original_counts.values, 
        color=['lightgreen', 'lightcoral'], alpha=0.7)
ax1.set_title('Original Dataset - Target Distribution\n(Highly Imbalanced)', 
              fontsize=14, fontweight='bold')
ax1.set_xlabel('Billing Status (0=On-time, 1=Late)')
ax1.set_ylabel('Count')
ax1.set_yscale('log')  # Log scale due to extreme imbalance
for i, v in enumerate(original_counts.values):
    ax1.text(i, v, f'{v:,}\n({original_pct.iloc[i]:.3f}%)', 
             ha='center', va='bottom', fontweight='bold')

# Balanced sample distribution
balanced_counts = df_balanced_new[target_var].value_counts().sort_index()
balanced_pct = df_balanced_new[target_var].value_counts(normalize=True).sort_index() * 100

ax2.bar(range(len(balanced_counts)), balanced_counts.values, 
        color=['lightgreen', 'lightcoral'], alpha=0.7)
ax2.set_title('Balanced Sample - Target Distribution\n(Well Balanced)', 
              fontsize=14, fontweight='bold')
ax2.set_xlabel('Billing Status (0=On-time, 1=Late)')
ax2.set_ylabel('Count')
for i, v in enumerate(balanced_counts.values):
    ax2.text(i, v, f'{v:,}\n({balanced_pct.iloc[i]:.1f}%)', 
             ha='center', va='bottom', fontweight='bold')

# Pie chart - Original
ax3.pie(original_counts.values, labels=['On-time', 'Late'], 
        colors=['lightgreen', 'lightcoral'], autopct='%1.3f%%', startangle=90)
ax3.set_title('Original Dataset\nDistribution', fontsize=12, fontweight='bold')

# Pie chart - Balanced
ax4.pie(balanced_counts.values, labels=['On-time', 'Late'], 
        colors=['lightgreen', 'lightcoral'], autopct='%1.1f%%', startangle=90)
ax4.set_title('Balanced Sample\nDistribution', fontsize=12, fontweight='bold')

plt.tight_layout()
plt.show()

# Summary comparison
print(f"\n{'='*60}")
print("              SUMMARY COMPARISON")
print(f"{'='*60}")

comparison_data = {
    'Metric': ['Total Records', 'On-time Billing', 'Late Billing', 'Balance Ratio'],
    'Original Dataset': [
        f"{len(df):,}",
        f"{original_counts[0]:,} ({original_pct[0]:.3f}%)",
        f"{original_counts[1]:,} ({original_pct[1]:.3f}%)",
        f"{original_counts[0]/original_counts[1]:.0f}:1"
    ],
    'Balanced Sample': [
        f"{len(df_balanced_new):,}",
        f"{balanced_counts[0.0]:,} ({balanced_pct[0.0]:.1f}%)",
        f"{balanced_counts[1.0]:,} ({balanced_pct[1.0]:.1f}%)",
        f"{balanced_counts[0.0]/balanced_counts[1.0]:.1f}:1"
    ]
}

comparison_df = pd.DataFrame(comparison_data)
print(comparison_df.to_string(index=False))

print(f"\n🎯 The balanced sample provides a much better foundation for building")
print(f"   a logistic regression model for predicting late billing!")
print("="*60)

# CATEGORICAL VARIABLES ANALYSIS - BALANCED DATASET
print("="*70)
print("         CATEGORICAL VARIABLES ANALYSIS - BALANCED DATASET")
print("="*70)

# Define categorical variables as per your specification
categorical_vars = ['Order_Type', 'Cust_AccGroup', 'Cust_Country', 'Cust_CreditRisk', 
                   'NetValue_Item_cat', 'Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']

# Use the balanced dataset
df_analysis = df_balanced_new.copy()
target = 'IsLate_Billed'

print(f"Working with balanced dataset: {df_analysis.shape[0]:,} rows")
print(f"Analyzing {len(categorical_vars)} categorical variables")
print(f"Target variable: {target}\n")

# Check which categorical variables exist in the dataset
existing_cat_vars = [var for var in categorical_vars if var in df_analysis.columns]
missing_cat_vars = [var for var in categorical_vars if var not in df_analysis.columns]

print(f"✅ Available categorical variables ({len(existing_cat_vars)}): {existing_cat_vars}")
if missing_cat_vars:
    print(f"❌ Missing categorical variables ({len(missing_cat_vars)}): {missing_cat_vars}")

print(f"\n{'='*70}")

# Analyze each categorical variable
for i, cat_var in enumerate(existing_cat_vars, 1):
    print(f"\n{i}. ANALYZING: {cat_var}")
    print(f"{'-'*50}")
    
    # Basic statistics
    unique_count = df_analysis[cat_var].nunique()
    missing_count = df_analysis[cat_var].isnull().sum()
    
    print(f"📊 Unique values: {unique_count}")
    print(f"❓ Missing values: {missing_count}")
    
    # Value counts
    value_counts = df_analysis[cat_var].value_counts()
    print(f"📈 Top categories:")
    for j, (category, count) in enumerate(value_counts.head().items(), 1):
        pct = count / len(df_analysis) * 100
        print(f"   {j}. {category}: {count:,} ({pct:.1f}%)")
    
    # Cross-tabulation with target
    crosstab = pd.crosstab(df_analysis[cat_var], df_analysis[target])
    crosstab_pct = pd.crosstab(df_analysis[cat_var], df_analysis[target], normalize='index') * 100
    
    print(f"\n📊 Relationship with Target Variable:")
    print(f"{'Category':<20} {'On-time':<10} {'Late':<10} {'Late %':<10}")
    print(f"{'-'*20} {'-'*10} {'-'*10} {'-'*10}")
    
    for category in crosstab.index[:5]:  # Show top 5 categories
        ontime = crosstab.loc[category, 0.0] if 0.0 in crosstab.columns else 0
        late = crosstab.loc[category, 1.0] if 1.0 in crosstab.columns else 0
        late_pct = crosstab_pct.loc[category, 1.0] if 1.0 in crosstab_pct.columns else 0
        
        category_str = str(category)[:17] + "..." if len(str(category)) > 20 else str(category)
        print(f"{category_str:<20} {ontime:<10} {late:<10} {late_pct:<9.1f}%")
    
    if len(crosstab.index) > 5:
        print(f"   ... and {len(crosstab.index) - 5} more categories")

print(f"\n{'='*70}")
print("CATEGORICAL ANALYSIS COMPLETE!")
print("="*70)

# LOGISTIC REGRESSION MODEL FOR ON-TIME vs LATE BILLING PREDICTION
print("="*80)
print("      LOGISTIC REGRESSION MODEL - ON-TIME vs LATE BILLING PREDICTION")
print("="*80)

# Import required libraries for modeling
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.preprocessing import OneHotEncoder
import warnings
warnings.filterwarnings('ignore')

# Define variables
identifier_col = 'VBELN'  # Updated based on actual column in balanced dataset
target_col = 'IsLate_Billed'

categorical_features = ['Order_Type', 'Cust_AccGroup', 'Cust_Country', 'Cust_CreditRisk', 
                       'NetValue_Item_cat', 'Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']

# Get numerical columns (exclude identifier and target)
exclude_cols = [identifier_col, target_col] + ['POSNR']  # POSNR might be another identifier
numerical_features = [col for col in df_analysis.columns 
                     if col not in categorical_features + exclude_cols 
                     and df_analysis[col].dtype in ['int64', 'float64']]

print(f"📊 Dataset shape: {df_analysis.shape}")
print(f"🎯 Target variable: {target_col}")
print(f"🔢 Numerical features ({len(numerical_features)}): {numerical_features}")
print(f"📂 Categorical features ({len(categorical_features)}): {categorical_features}")

# Check for missing values
print(f"\n{'='*50}")
print("DATA QUALITY CHECK")
print(f"{'='*50}")

missing_summary = df_analysis[numerical_features + categorical_features + [target_col]].isnull().sum()
missing_features = missing_summary[missing_summary > 0]

if len(missing_features) > 0:
    print("❗ Features with missing values:")
    for feature, missing_count in missing_features.items():
        missing_pct = missing_count / len(df_analysis) * 100
        print(f"   {feature}: {missing_count} ({missing_pct:.1f}%)")
    
    # Handle missing values for numerical features
    print("\n🔧 Handling missing values in numerical features...")
    for col in numerical_features:
        if df_analysis[col].isnull().sum() > 0:
            median_val = df_analysis[col].median()
            df_analysis[col].fillna(median_val, inplace=True)
            print(f"   Filled {col} with median: {median_val:.4f}")
    
    # Handle missing values for categorical features
    print("\n🔧 Handling missing values in categorical features...")
    for col in categorical_features:
        if col in df_analysis.columns and df_analysis[col].isnull().sum() > 0:
            mode_val = df_analysis[col].mode().iloc[0] if len(df_analysis[col].mode()) > 0 else 'Unknown'
            df_analysis[col].fillna(mode_val, inplace=True)
            print(f"   Filled {col} with mode: {mode_val}")
else:
    print("✅ No missing values found!")

# Prepare features for modeling
print(f"\n{'='*50}")
print("FEATURE PREPARATION")
print(f"{'='*50}")

# Prepare numerical features
X_numerical = df_analysis[numerical_features].copy()
print(f"📊 Numerical features prepared: {X_numerical.shape}")

# Prepare categorical features with one-hot encoding
X_categorical_list = []
categorical_feature_names = []

for cat_feature in categorical_features:
    if cat_feature in df_analysis.columns:
        # One-hot encode
        encoded = pd.get_dummies(df_analysis[cat_feature], prefix=cat_feature, drop_first=True)
        X_categorical_list.append(encoded)
        categorical_feature_names.extend(encoded.columns.tolist())

if X_categorical_list:
    X_categorical = pd.concat(X_categorical_list, axis=1)
    print(f"📂 Categorical features after encoding: {X_categorical.shape}")
    
    # Combine numerical and categorical features
    X = pd.concat([X_numerical, X_categorical], axis=1)
else:
    X = X_numerical.copy()

# Prepare target variable
y = df_analysis[target_col].copy()

print(f"🎯 Final feature matrix: {X.shape}")
print(f"🎯 Target variable: {y.shape}")
print(f"🎯 Target distribution: {y.value_counts().sort_index().to_dict()}")

# Split the data
print(f"\n{'='*50}")
print("TRAIN-TEST SPLIT")
print(f"{'='*50}")

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.3, random_state=42, stratify=y
)

print(f"📊 Training set: {X_train.shape[0]:,} samples")
print(f"📊 Test set: {X_test.shape[0]:,} samples")
print(f"🎯 Training target distribution: {y_train.value_counts().sort_index().to_dict()}")
print(f"🎯 Test target distribution: {y_test.value_counts().sort_index().to_dict()}")

# Feature scaling
print(f"\n{'='*50}")
print("FEATURE SCALING")
print(f"{'='*50}")

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print("✅ Features scaled using StandardScaler")
print(f"📊 Scaled training features shape: {X_train_scaled.shape}")
print(f"📊 Scaled test features shape: {X_test_scaled.shape}")

# Train Logistic Regression Model
print(f"\n{'='*50}")
print("LOGISTIC REGRESSION MODEL TRAINING")
print(f"{'='*50}")

# Train model
lr_model = LogisticRegression(random_state=42, max_iter=1000)
lr_model.fit(X_train_scaled, y_train)

print("✅ Logistic Regression model trained successfully!")

# Make predictions
y_pred = lr_model.predict(X_test_scaled)
y_pred_proba = lr_model.predict_proba(X_test_scaled)[:, 1]

print(f"📊 Predictions completed")
print(f"🎯 Predicted distribution: {pd.Series(y_pred).value_counts().sort_index().to_dict()}")

print("\n🎯 LOGISTIC REGRESSION MODEL TRAINING COMPLETE!")
print("="*80)

# MODEL EVALUATION AND PERFORMANCE METRICS
print("="*80)
print("                    MODEL EVALUATION & PERFORMANCE")
print("="*80)

# Calculate performance metrics
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
auc_score = roc_auc_score(y_test, y_pred_proba)

print(f"📊 MODEL PERFORMANCE METRICS")
print(f"{'='*40}")
print(f"🎯 Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)")
print(f"🎯 Precision: {precision:.4f} ({precision*100:.2f}%)")
print(f"🎯 Recall:    {recall:.4f} ({recall*100:.2f}%)")
print(f"🎯 F1-Score:  {f1:.4f} ({f1*100:.2f}%)")
print(f"🎯 AUC-ROC:   {auc_score:.4f} ({auc_score*100:.2f}%)")

# Confusion Matrix
print(f"\n📊 CONFUSION MATRIX")
print(f"{'='*40}")
cm = confusion_matrix(y_test, y_pred)
print(f"                 Predicted")
print(f"              On-time  Late")
print(f"Actual On-time   {cm[0,0]:4d}  {cm[0,1]:4d}")
print(f"       Late      {cm[1,0]:4d}  {cm[1,1]:4d}")

# Calculate additional metrics
tn, fp, fn, tp = cm.ravel()
specificity = tn / (tn + fp)
sensitivity = tp / (tp + fn)  # Same as recall

print(f"\n📊 DETAILED METRICS")
print(f"{'='*40}")
print(f"True Positives (Late correctly predicted):  {tp}")
print(f"True Negatives (On-time correctly predicted): {tn}")
print(f"False Positives (Late incorrectly predicted): {fp}")
print(f"False Negatives (On-time incorrectly predicted): {fn}")
print(f"Sensitivity (True Positive Rate): {sensitivity:.4f}")
print(f"Specificity (True Negative Rate): {specificity:.4f}")

# Classification Report
print(f"\n📊 DETAILED CLASSIFICATION REPORT")
print(f"{'='*50}")
print(classification_report(y_test, y_pred, 
                          target_names=['On-time Billing', 'Late Billing']))

# Visualizations
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# 1. Confusion Matrix Heatmap
import seaborn as sns
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['On-time', 'Late'],
            yticklabels=['On-time', 'Late'], ax=ax1)
ax1.set_title('Confusion Matrix', fontsize=14, fontweight='bold')
ax1.set_xlabel('Predicted')
ax1.set_ylabel('Actual')

# 2. ROC Curve
fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)
ax2.plot(fpr, tpr, color='red', linewidth=2, label=f'ROC Curve (AUC = {auc_score:.3f})')
ax2.plot([0, 1], [0, 1], color='blue', linestyle='--', alpha=0.5)
ax2.set_xlabel('False Positive Rate')
ax2.set_ylabel('True Positive Rate')
ax2.set_title('ROC Curve', fontsize=14, fontweight='bold')
ax2.legend()
ax2.grid(True, alpha=0.3)

# 3. Prediction Probability Distribution
ax3.hist(y_pred_proba[y_test == 0], bins=30, alpha=0.7, label='On-time Billing', color='green')
ax3.hist(y_pred_proba[y_test == 1], bins=30, alpha=0.7, label='Late Billing', color='red')
ax3.axvline(x=0.5, color='black', linestyle='--', alpha=0.8, label='Threshold (0.5)')
ax3.set_xlabel('Predicted Probability of Late Billing')
ax3.set_ylabel('Frequency')
ax3.set_title('Prediction Probability Distribution', fontsize=14, fontweight='bold')
ax3.legend()
ax3.grid(True, alpha=0.3)

# 4. Feature Importance (Top 15)
feature_names = X.columns.tolist()
feature_importance = abs(lr_model.coef_[0])
importance_df = pd.DataFrame({
    'Feature': feature_names,
    'Importance': feature_importance
}).sort_values('Importance', ascending=False)

top_features = importance_df.head(15)
ax4.barh(range(len(top_features)), top_features['Importance'], color='skyblue')
ax4.set_yticks(range(len(top_features)))
ax4.set_yticklabels(top_features['Feature'])
ax4.set_xlabel('Absolute Coefficient Value')
ax4.set_title('Top 15 Most Important Features', fontsize=14, fontweight='bold')
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Model Interpretation
print(f"\n{'='*80}")
print("                    MODEL INTERPRETATION")
print(f"{'='*80}")

print(f"📊 TOP 10 MOST IMPORTANT FEATURES:")
print(f"{'='*50}")
print(f"{'Rank':<5} {'Feature':<30} {'Coefficient':<12} {'Impact'}")
print(f"{'-'*5} {'-'*30} {'-'*12} {'-'*20}")

for i, (_, row) in enumerate(importance_df.head(10).iterrows(), 1):
    coef_val = lr_model.coef_[0][feature_names.index(row['Feature'])]
    impact = "Increases Late Risk" if coef_val > 0 else "Decreases Late Risk"
    print(f"{i:<5} {row['Feature']:<30} {coef_val:<11.4f} {impact}")

# Model Summary
print(f"\n{'='*80}")
print("                         MODEL SUMMARY")
print(f"{'='*80}")

print(f"🎯 Model Type: Logistic Regression")
print(f"📊 Dataset: Balanced Sample ({len(df_analysis):,} records)")
print(f"🔢 Features: {X.shape[1]} total ({len(numerical_features)} numerical + {len(categorical_feature_names)} categorical)")
print(f"📈 Performance: {accuracy*100:.1f}% accuracy, {auc_score*100:.1f}% AUC")

if accuracy >= 0.80:
    performance_rating = "🟢 EXCELLENT"
elif accuracy >= 0.70:
    performance_rating = "🟡 GOOD"
elif accuracy >= 0.60:
    performance_rating = "🟠 FAIR"
else:
    performance_rating = "🔴 NEEDS IMPROVEMENT"

print(f"⭐ Overall Performance: {performance_rating}")

print(f"\n🎯 BILLING PREDICTION MODEL COMPLETE!")
print("="*80)

# Missing values analysis
missing_data = df.isnull().sum()
missing_percent = (missing_data / len(df)) * 100

missing_df = pd.DataFrame({
    'Column': missing_data.index,
    'Missing_Count': missing_data.values,
    'Missing_Percentage': missing_percent.values
})

missing_df = missing_df[missing_df['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)

print("Missing Values Summary:")
if len(missing_df) > 0:
    display(missing_df)
else:
    print("No missing values found!")

# Visualize missing values if any
if len(missing_df) > 0:
    plt.figure(figsize=(12, 6))
    sns.barplot(data=missing_df.head(20), x='Missing_Percentage', y='Column')
    plt.title('Top 20 Columns with Missing Values')
    plt.xlabel('Missing Percentage (%)')
    plt.tight_layout()
    plt.show()
else:
    print("No missing values to visualize")

# Check for duplicates
duplicate_count = df.duplicated().sum()
print(f"Number of duplicate rows: {duplicate_count}")

# Check for duplicate case keys
if 'CASE_KEY' in df.columns:
    duplicate_casekeys = df['CASE_KEY'].duplicated().sum()
    print(f"Number of duplicate CASE_KEYs: {duplicate_casekeys}")
    print(f"Unique CASE_KEYs: {df['CASE_KEY'].nunique():,}")

# Target variable analysis
if 'IsLate_Billed' in df.columns:
    target_var = 'IsLate_Billed'
    
    print(f"Target Variable: {target_var}")
    print(f"Data type: {df[target_var].dtype}")
    print(f"\nValue counts:")
    target_counts = df[target_var].value_counts()
    print(target_counts)
    
    print(f"\nPercentage distribution:")
    target_pct = df[target_var].value_counts(normalize=True) * 100
    print(target_pct)
    
    # Visualize target distribution
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Count plot
    sns.countplot(data=df, x=target_var, ax=ax1)
    ax1.set_title('Target Variable Distribution (Count)')
    
    # Percentage plot
    target_pct.plot(kind='bar', ax=ax2)
    ax2.set_title('Target Variable Distribution (Percentage)')
    ax2.set_ylabel('Percentage')
    
    plt.tight_layout()
    plt.show()
    
    # Class imbalance check
    if len(target_counts) == 2:
        imbalance_ratio = target_counts.min() / target_counts.max()
        print(f"\nClass imbalance ratio: {imbalance_ratio:.3f}")
        if imbalance_ratio < 0.3:
            print("⚠️ Significant class imbalance detected. Consider using appropriate techniques.")
else:
    print("Target variable 'IsLate_Billed' not found in the dataset")

# Define categorical columns
categorical_cols = ['Order_Type', 'Cust_AccGroup', 'Cust_Country', 'Cust_CreditRisk', 
                   'NetValue_Item_cat', 'Has_CreditBlock', 'Has_DeliveryBlock', 'Has_BillingBlock']

# Filter existing categorical columns
existing_cat_cols = [col for col in categorical_cols if col in df.columns]
print(f"Categorical columns found: {existing_cat_cols}")

# Analyze each categorical variable
for col in existing_cat_cols:
    print(f"\n{'='*50}")
    print(f"Categorical Variable: {col}")
    print(f"{'='*50}")
    
    print(f"Data type: {df[col].dtype}")
    print(f"Unique values: {df[col].nunique()}")
    print(f"\nValue counts:")
    print(df[col].value_counts().head(10))
    
    if df[col].nunique() > 10:
        print(f"\n⚠️ High cardinality: {df[col].nunique()} unique values")

# Visualize categorical variables
if existing_cat_cols:
    # Calculate optimal subplot layout
    n_cols = min(3, len(existing_cat_cols))
    n_rows = (len(existing_cat_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
    if n_rows == 1:
        axes = [axes] if n_cols == 1 else axes
    else:
        axes = axes.flatten()
    
    for idx, col in enumerate(existing_cat_cols):
        if df[col].nunique() <= 20:  # Only plot if reasonable number of categories
            df[col].value_counts().head(15).plot(kind='bar', ax=axes[idx])
            axes[idx].set_title(f'{col} Distribution')
            axes[idx].tick_params(axis='x', rotation=45)
        else:
            axes[idx].text(0.5, 0.5, f'{col}\n{df[col].nunique()} unique values\n(Too many to display)', 
                         ha='center', va='center', transform=axes[idx].transAxes)
            axes[idx].set_title(f'{col} (High Cardinality)')
    
    # Hide empty subplots
    for idx in range(len(existing_cat_cols), len(axes)):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    plt.show()

# Analyze relationship between categorical variables and target
if 'IsLate_Billed' in df.columns and existing_cat_cols:
    target_var = 'IsLate_Billed'
    
    for col in existing_cat_cols:
        print(f"\n{'='*50}")
        print(f"Analysis: {col} vs {target_var}")
        print(f"{'='*50}")
        
        # Cross-tabulation
        crosstab = pd.crosstab(df[col], df[target_var], margins=True)
        print("Cross-tabulation:")
        print(crosstab)
        
        # Percentage distribution
        crosstab_pct = pd.crosstab(df[col], df[target_var], normalize='index') * 100
        print("\nPercentage distribution (row-wise):")
        print(crosstab_pct.round(2))
        
        # Chi-square test
        try:
            chi2, p_value, dof, expected = stats.chi2_contingency(crosstab.iloc[:-1, :-1])
            print(f"\nChi-square test:")
            print(f"Chi-square statistic: {chi2:.4f}")
            print(f"P-value: {p_value:.6f}")
            print(f"Degrees of freedom: {dof}")
            
            if p_value < 0.05:
                print("✅ Significant association with target variable (p < 0.05)")
            else:
                print("❌ No significant association with target variable (p >= 0.05)")
        except:
            print("\n⚠️ Chi-square test could not be performed")

# Visualize categorical vs target relationships
if 'IsLate_Billed' in df.columns and existing_cat_cols:
    target_var = 'IsLate_Billed'
    
    # Select categorical variables with reasonable cardinality for visualization
    vis_cat_cols = [col for col in existing_cat_cols if df[col].nunique() <= 15]
    
    if vis_cat_cols:
        n_cols = min(2, len(vis_cat_cols))
        n_rows = (len(vis_cat_cols) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 6*n_rows))
        if n_rows == 1:
            axes = [axes] if n_cols == 1 else axes
        else:
            axes = axes.flatten()
        
        for idx, col in enumerate(vis_cat_cols):
            # Stacked bar plot
            crosstab_pct = pd.crosstab(df[col], df[target_var], normalize='index') * 100
            crosstab_pct.plot(kind='bar', stacked=True, ax=axes[idx])
            axes[idx].set_title(f'{col} vs {target_var} (Percentage)')
            axes[idx].set_ylabel('Percentage')
            axes[idx].tick_params(axis='x', rotation=45)
            axes[idx].legend(title=target_var)
        
        # Hide empty subplots
        for idx in range(len(vis_cat_cols), len(axes)):
            axes[idx].set_visible(False)
        
        plt.tight_layout()
        plt.show()

# Identify numerical columns
exclude_cols = ['CASE_KEY'] + existing_cat_cols + (['IsLate_Billed'] if 'IsLate_Billed' in df.columns else [])
numerical_cols = [col for col in df.columns if col not in exclude_cols and df[col].dtype in ['int64', 'float64']]

print(f"Numerical columns identified: {len(numerical_cols)}")
print(f"Columns: {numerical_cols[:10]}...") if len(numerical_cols) > 10 else print(f"Columns: {numerical_cols}")

if numerical_cols:
    # Basic statistics
    print("\nBasic Statistics for Numerical Variables:")
    numeric_stats = df[numerical_cols].describe()
    display(numeric_stats)

# Distribution analysis for numerical variables
if numerical_cols:
    # Select first 12 numerical columns for visualization
    vis_num_cols = numerical_cols[:12]
    
    n_cols = 3
    n_rows = (len(vis_num_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes
    
    for idx, col in enumerate(vis_num_cols):
        df[col].hist(bins=30, ax=axes[idx], alpha=0.7)
        axes[idx].set_title(f'Distribution of {col}')
        axes[idx].set_xlabel(col)
        axes[idx].set_ylabel('Frequency')
    
    # Hide empty subplots
    for idx in range(len(vis_num_cols), len(axes)):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    plt.show()

# Outlier detection using IQR method
if numerical_cols:
    outlier_summary = []
    
    for col in numerical_cols[:20]:  # Analyze first 20 numerical columns
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        outlier_count = len(outliers)
        outlier_percentage = (outlier_count / len(df)) * 100
        
        outlier_summary.append({
            'Column': col,
            'Outlier_Count': outlier_count,
            'Outlier_Percentage': outlier_percentage,
            'Q1': Q1,
            'Q3': Q3,
            'IQR': IQR
        })
    
    outlier_df = pd.DataFrame(outlier_summary)
    outlier_df = outlier_df.sort_values('Outlier_Percentage', ascending=False)
    
    print("Outlier Analysis (Top 15 columns):")
    display(outlier_df.head(15))

# Correlation analysis
if numerical_cols:
    # Calculate correlation matrix for numerical variables
    correlation_matrix = df[numerical_cols].corr()
    
    # Plot correlation heatmap (top 20 variables)
    top_20_num_cols = numerical_cols[:20]
    if len(top_20_num_cols) > 1:
        plt.figure(figsize=(15, 12))
        mask = np.triu(np.ones_like(correlation_matrix.loc[top_20_num_cols, top_20_num_cols], dtype=bool))
        sns.heatmap(correlation_matrix.loc[top_20_num_cols, top_20_num_cols], 
                   mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('Correlation Heatmap (Top 20 Numerical Variables)')
        plt.tight_layout()
        plt.show()
    
    # Find highly correlated pairs
    high_corr_pairs = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_value = correlation_matrix.iloc[i, j]
            if abs(corr_value) > 0.7:  # High correlation threshold
                high_corr_pairs.append({
                    'Variable_1': correlation_matrix.columns[i],
                    'Variable_2': correlation_matrix.columns[j],
                    'Correlation': corr_value
                })
    
    if high_corr_pairs:
        high_corr_df = pd.DataFrame(high_corr_pairs)
        high_corr_df = high_corr_df.sort_values('Correlation', key=abs, ascending=False)
        print("\nHighly Correlated Variable Pairs (|correlation| > 0.7):")
        display(high_corr_df.head(20))
    else:
        print("\nNo highly correlated pairs found (|correlation| > 0.7)")

# Analyze relationship between numerical variables and target
if 'IsLate_Billed' in df.columns and numerical_cols:
    target_var = 'IsLate_Billed'
    
    # Statistical tests for numerical vs target
    statistical_results = []
    
    for col in numerical_cols[:20]:  # Analyze first 20 numerical columns
        # Separate data by target classes
        group_0 = df[df[target_var] == 0][col].dropna()
        group_1 = df[df[target_var] == 1][col].dropna()
        
        if len(group_0) > 0 and len(group_1) > 0:
            # T-test
            t_stat, p_value = stats.ttest_ind(group_0, group_1)
            
            # Effect size (Cohen's d)
            pooled_std = np.sqrt(((len(group_0) - 1) * group_0.var() + (len(group_1) - 1) * group_1.var()) / 
                               (len(group_0) + len(group_1) - 2))
            cohens_d = (group_1.mean() - group_0.mean()) / pooled_std if pooled_std != 0 else 0
            
            statistical_results.append({
                'Variable': col,
                'Mean_Class_0': group_0.mean(),
                'Mean_Class_1': group_1.mean(),
                'T_Statistic': t_stat,
                'P_Value': p_value,
                'Cohens_D': cohens_d,
                'Significant': 'Yes' if p_value < 0.05 else 'No'
            })
    
    if statistical_results:
        stats_df = pd.DataFrame(statistical_results)
        stats_df = stats_df.sort_values('P_Value')
        
        print("Statistical Analysis: Numerical Variables vs Target")
        print("(Sorted by p-value, showing most significant first)")
        display(stats_df.head(15))
        
        # Count significant variables
        significant_count = (stats_df['P_Value'] < 0.05).sum()
        print(f"\nSignificant variables (p < 0.05): {significant_count} out of {len(stats_df)}")

# Box plots for top significant numerical variables
if 'IsLate_Billed' in df.columns and numerical_cols and 'stats_df' in locals():
    target_var = 'IsLate_Billed'
    
    # Get top 8 most significant variables
    top_significant = stats_df.head(8)['Variable'].tolist()
    
    if top_significant:
        n_cols = 2
        n_rows = (len(top_significant) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
        axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes
        
        for idx, col in enumerate(top_significant):
            sns.boxplot(data=df, x=target_var, y=col, ax=axes[idx])
            axes[idx].set_title(f'{col} by {target_var}')
        
        # Hide empty subplots
        for idx in range(len(top_significant), len(axes)):
            axes[idx].set_visible(False)
        
        plt.tight_layout()
        plt.show()

# Feature engineering recommendations
print("FEATURE ENGINEERING RECOMMENDATIONS")
print("="*50)

# 1. Categorical variables
print("\n1. CATEGORICAL VARIABLES:")
for col in existing_cat_cols:
    unique_count = df[col].nunique()
    if unique_count == 2:
        print(f"   • {col}: Binary variable - use Label Encoding")
    elif unique_count <= 10:
        print(f"   • {col}: Low cardinality ({unique_count} values) - use One-Hot Encoding")
    elif unique_count <= 50:
        print(f"   • {col}: Medium cardinality ({unique_count} values) - consider Target Encoding or Feature Hashing")
    else:
        print(f"   • {col}: High cardinality ({unique_count} values) - consider Target Encoding, Embedding, or grouping rare categories")

# 2. Numerical variables
print("\n2. NUMERICAL VARIABLES:")
if 'outlier_df' in locals():
    high_outlier_cols = outlier_df[outlier_df['Outlier_Percentage'] > 5]['Column'].tolist()
    if high_outlier_cols:
        print(f"   • Variables with high outliers (>5%): {len(high_outlier_cols)} variables")
        print(f"     Consider: Robust scaling, log transformation, or outlier treatment")

# 3. Missing values
print("\n3. MISSING VALUES:")
if 'missing_df' in locals() and len(missing_df) > 0:
    print(f"   • {len(missing_df)} variables have missing values")
    print(f"   • Consider: Imputation strategies based on variable type and missingness pattern")
else:
    print(f"   • No missing values detected - Good data quality!")

# 4. Correlation
print("\n4. MULTICOLLINEARITY:")
if 'high_corr_df' in locals() and len(high_corr_df) > 0:
    print(f"   • {len(high_corr_df)} highly correlated pairs found")
    print(f"   • Consider: Remove one variable from each pair or use PCA")
else:
    print(f"   • No high multicollinearity detected")

# 5. Class imbalance
print("\n5. CLASS IMBALANCE:")
if 'IsLate_Billed' in df.columns:
    target_counts = df['IsLate_Billed'].value_counts()
    if len(target_counts) == 2:
        imbalance_ratio = target_counts.min() / target_counts.max()
        if imbalance_ratio < 0.3:
            print(f"   • Imbalance ratio: {imbalance_ratio:.3f} - Consider SMOTE, class weights, or threshold tuning")
        else:
            print(f"   • Imbalance ratio: {imbalance_ratio:.3f} - Acceptable balance")

print("\n" + "="*50)

# Model preparation summary
print("LOGISTIC REGRESSION MODEL PREPARATION SUMMARY")
print("="*60)

print(f"\nDATASET OVERVIEW:")
print(f"• Total records: {len(df):,}")
print(f"• Total features: {len(df.columns)}")
print(f"• Categorical features: {len(existing_cat_cols)}")
print(f"• Numerical features: {len(numerical_cols)}")

if 'IsLate_Billed' in df.columns:
    print(f"\nTARGET VARIABLE:")
    target_dist = df['IsLate_Billed'].value_counts(normalize=True) * 100
    print(f"• Target: IsLate_Billed")
    for value, pct in target_dist.items():
        print(f"  - Class {value}: {pct:.1f}%")

print(f"\nNEXT STEPS FOR MODEL BUILDING:")
print(f"1. Data Preprocessing:")
print(f"   - Handle missing values (if any)")
print(f"   - Encode categorical variables")
print(f"   - Scale numerical features")
print(f"   - Handle outliers (if necessary)")

print(f"\n2. Feature Selection:")
print(f"   - Remove highly correlated features")
print(f"   - Use statistical significance results")
print(f"   - Consider domain knowledge")

print(f"\n3. Model Training:")
print(f"   - Split data (train/validation/test)")
print(f"   - Train logistic regression model")
print(f"   - Tune hyperparameters")
print(f"   - Handle class imbalance (if needed)")

print(f"\n4. Model Evaluation:")
print(f"   - Accuracy, Precision, Recall, F1-score")
print(f"   - ROC-AUC score")
print(f"   - Confusion matrix")
print(f"   - Feature importance analysis")

print("\n" + "="*60)