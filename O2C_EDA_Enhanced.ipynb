# Install required packages (Colab only)
!pip install imbalanced-learn shap --quiet

# Import essential libraries
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import warnings
from scipy import stats

# Machine Learning
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve

# Imbalanced learning
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import Pipeline

# Explainability
import shap

# Configuration
plt.style.use('default')
sns.set_palette("husl")
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)

print("✅ Environment setup complete!")

# Load the dataset
df = pd.read_csv("On-time_billing.csv")

print(f"📊 Dataset loaded successfully!")
print(f"📏 Dataset shape: {df.shape}")
print(f"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Initial data overview
print("🔍 DATASET OVERVIEW")
print("=" * 50)
print(f"Rows: {df.shape[0]:,}")
print(f"Columns: {df.shape[1]}")
print(f"\n📋 Column Information:")
print(df.dtypes.value_counts())

print(f"\n📊 First 5 rows:")
display(df.head())

print(f"\n📈 Dataset Info:")
df.info()

# Store original shape for comparison
original_shape = df.shape

# Drop identifier columns (existing approach)
identifier_cols = ["VBELN", "POSNR"]
df.drop(columns=identifier_cols, inplace=True, errors='ignore')

print(f"🗑️ Dropped identifier columns: {identifier_cols}")
print(f"📏 Shape after dropping identifiers: {df.shape}")

# Handle blank spaces in CT columns (existing approach)
print("🔄 Cleaning blank spaces...")
df.replace(r'^\s*$', np.nan, regex=True, inplace=True)
df = df.apply(pd.to_numeric, errors='ignore')

print("✅ Blank space cleaning completed")

# Enhanced missing values analysis
missing_data = df.isnull().sum().sort_values(ascending=False)
missing_percent = (missing_data / len(df)) * 100

missing_df = pd.DataFrame({
    'Missing_Count': missing_data,
    'Missing_Percentage': missing_percent
})

print("🕳️ MISSING VALUES ANALYSIS")
print("=" * 40)
print(missing_df[missing_df['Missing_Count'] > 0])

# Visualize missing values
fig, axes = plt.subplots(2, 1, figsize=(14, 10))

# Missing values heatmap
sns.heatmap(df.isnull(), cbar=True, cmap="viridis", yticklabels=False, ax=axes[0])
axes[0].set_title("🔥 Missing Values Heatmap", fontsize=14, fontweight='bold')

# Missing values by column
missing_cols = missing_df[missing_df['Missing_Count'] > 0]
if len(missing_cols) > 0:
    sns.barplot(data=missing_cols.reset_index(), x='Missing_Percentage', y='index', ax=axes[1])
    axes[1].set_title("📊 Missing Values by Column (%)", fontsize=14, fontweight='bold')
    axes[1].set_xlabel("Missing Percentage")
else:
    axes[1].text(0.5, 0.5, 'No Missing Values Found!', 
                 horizontalalignment='center', verticalalignment='center', 
                 transform=axes[1].transAxes, fontsize=16, fontweight='bold')
    axes[1].axis('off')

plt.tight_layout()
plt.show()

# Encode categorical variables (existing approach)
cat_cols = df.select_dtypes(include='object').columns.tolist()
le_dict = {}

print(f"🔄 Encoding {len(cat_cols)} categorical columns...")
print(f"Categorical columns: {cat_cols}")

for col in cat_cols:
    le = LabelEncoder()
    df[col] = le.fit_transform(df[col].astype(str))
    le_dict[col] = le
    print(f"  ✅ {col}: {len(le.classes_)} unique categories")

print("\n🎯 Label encoding completed!")

# Handle missing values (existing approach)
print("🧼 Handling missing values with median imputation...")
df.fillna(df.median(numeric_only=True), inplace=True)

# Verify no missing values remain
remaining_missing = df.isnull().sum().sum()
print(f"✅ Missing values after cleaning: {remaining_missing}")

print(f"\n📏 Final dataset shape: {df.shape}")

# Target variable comprehensive analysis
target_col = 'IsLate_Billed'
target_dist = df[target_col].value_counts()
target_prop = df[target_col].value_counts(normalize=True)

print("🎯 TARGET VARIABLE ANALYSIS")
print("=" * 40)
print(f"Target column: {target_col}")
print(f"\nDistribution:")
for idx, (val, count) in enumerate(target_dist.items()):
    print(f"  Class {val}: {count:,} ({target_prop.iloc[idx]:.2%})")

# Calculate class imbalance ratio
imbalance_ratio = target_dist.max() / target_dist.min()
print(f"\n⚖️ Class imbalance ratio: {imbalance_ratio:.2f}:1")

# Enhanced visualization
fig, axes = plt.subplots(1, 3, figsize=(18, 5))

# Count plot with annotations
sns.countplot(data=df, x=target_col, ax=axes[0])
axes[0].set_title("🎯 Target Distribution (Counts)", fontweight='bold')
axes[0].set_xlabel("Late Billed (0=No, 1=Yes)")
for i, v in enumerate(target_dist.values):
    axes[0].text(i, v + 50, f'{v:,}\n({target_prop.iloc[i]:.1%})', 
                ha='center', va='bottom', fontweight='bold')

# Pie chart
colors = ['lightgreen', 'lightcoral']
axes[1].pie(target_dist.values, labels=['On-time', 'Late'], autopct='%1.1f%%', 
           colors=colors, startangle=90)
axes[1].set_title("🥧 Target Distribution", fontweight='bold')

# Imbalance bar chart
axes[2].bar(['On-time', 'Late'], target_dist.values, color=colors)
axes[2].set_title(f"⚖️ Class Imbalance\n({imbalance_ratio:.1f}:1 ratio)", fontweight='bold')
axes[2].set_ylabel('Count')

plt.tight_layout()
plt.show()

# Separate features by type
numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
if target_col in numeric_cols:
    numeric_cols.remove(target_col)

categorical_cols = [col for col in df.columns if col not in numeric_cols and col != target_col]

print("📊 FEATURE BREAKDOWN")
print("=" * 30)
print(f"Numeric features: {len(numeric_cols)}")
print(f"Categorical features: {len(categorical_cols)}")
print(f"Total features: {len(numeric_cols) + len(categorical_cols)}")

# Statistical summary for numeric features
if len(numeric_cols) > 0:
    print("\n📈 NUMERIC FEATURES SUMMARY")
    numeric_summary = df[numeric_cols].describe()
    display(numeric_summary)
    
    # Skewness analysis
    print("\n📉 SKEWNESS ANALYSIS")
    skewness = df[numeric_cols].skew().sort_values(key=abs, ascending=False)
    highly_skewed = skewness[abs(skewness) > 2]
    
    print("Feature Skewness:")
    for feature, skew_val in skewness.items():
        skew_level = "Highly Skewed" if abs(skew_val) > 2 else "Moderately Skewed" if abs(skew_val) > 1 else "Normal"
        print(f"  {feature}: {skew_val:.3f} ({skew_level})")
    
    if len(highly_skewed) > 0:
        print(f"\n⚠️ Highly skewed features: {list(highly_skewed.index)}")

# Enhanced numeric feature distributions
if len(numeric_cols) > 0:
    n_cols = min(4, len(numeric_cols))
    n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes]
    
    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            sns.histplot(data=df, x=col, kde=True, ax=axes[i], alpha=0.7)
            skew_val = df[col].skew()
            axes[i].set_title(f"📊 {col}\nSkewness: {skew_val:.2f}", fontweight='bold')
            axes[i].grid(True, alpha=0.3)
            
            # Add mean and median lines
            mean_val = df[col].mean()
            median_val = df[col].median()
            axes[i].axvline(mean_val, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.2f}')
            axes[i].axvline(median_val, color='blue', linestyle='--', alpha=0.7, label=f'Median: {median_val:.2f}')
            axes[i].legend()
    
    # Hide empty subplots
    for i in range(len(numeric_cols), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle("📈 Numeric Features Distribution Analysis", fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()

# Enhanced categorical feature analysis
if len(categorical_cols) > 0:
    print("🏷️ CATEGORICAL FEATURES ANALYSIS")
    print("=" * 35)
    
    # Display categorical feature info
    cat_info = []
    for col in categorical_cols:
        unique_count = df[col].nunique()
        most_frequent = df[col].value_counts().index[0]
        most_frequent_count = df[col].value_counts().iloc[0]
        most_frequent_pct = (most_frequent_count / len(df)) * 100
        
        cat_info.append({
            'Feature': col,
            'Unique_Values': unique_count,
            'Most_Frequent': most_frequent,
            'Most_Frequent_Count': most_frequent_count,
            'Most_Frequent_Pct': most_frequent_pct
        })
    
    cat_df = pd.DataFrame(cat_info)
    display(cat_df)
    
    # Visualize categorical distributions
    n_cols = min(3, len(categorical_cols))
    n_rows = (len(categorical_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes]
    
    for i, col in enumerate(categorical_cols):
        if i < len(axes):
            value_counts = df[col].value_counts()
            unique_count = df[col].nunique()
            
            # Show top categories if too many
            if unique_count > 10:
                top_categories = value_counts.head(10)
                sns.barplot(x=top_categories.values, y=top_categories.index, ax=axes[i])
                axes[i].set_title(f"📊 {col}\n(Top 10 of {unique_count} categories)", fontweight='bold')
            else:
                sns.countplot(data=df, y=col, order=value_counts.index, ax=axes[i])
                axes[i].set_title(f"📊 {col}\n({unique_count} categories)", fontweight='bold')
            
            axes[i].grid(True, alpha=0.3)
    
    # Hide empty subplots
    for i in range(len(categorical_cols), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle("🏷️ Categorical Features Distribution", fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()

# Comprehensive outlier analysis
if len(numeric_cols) > 0:
    print("🎯 OUTLIER ANALYSIS")
    print("=" * 25)
    
    outlier_summary = []
    
    for col in numeric_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        outlier_count = len(outliers)
        outlier_percent = (outlier_count / len(df)) * 100
        
        outlier_summary.append({
            'Feature': col,
            'Outlier_Count': outlier_count,
            'Outlier_Percentage': outlier_percent,
            'Lower_Bound': lower_bound,
            'Upper_Bound': upper_bound
        })
    
    outlier_df = pd.DataFrame(outlier_summary).sort_values('Outlier_Percentage', ascending=False)
    display(outlier_df)
    
    # Boxplot visualizations
    n_cols = min(4, len(numeric_cols))
    n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 4*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes]
    
    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            outlier_info = outlier_df[outlier_df['Feature'] == col].iloc[0]
            sns.boxplot(data=df, x=col, ax=axes[i])
            axes[i].set_title(f"📦 {col}\nOutliers: {outlier_info['Outlier_Count']} ({outlier_info['Outlier_Percentage']:.1f}%)", 
                            fontweight='bold')
            axes[i].grid(True, alpha=0.3)
    
    # Hide empty subplots
    for i in range(len(numeric_cols), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle("📦 Outlier Analysis - Box Plots", fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()

# Enhanced correlation analysis
if len(numeric_cols) > 1:
    # Calculate correlation matrix
    corr_matrix = df[numeric_cols + [target_col]].corr()
    
    # Create correlation heatmap
    plt.figure(figsize=(14, 12))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, annot=True, cmap='RdBu_r', center=0, 
                square=True, mask=mask, fmt='.2f', 
                cbar_kws={"shrink": .8})
    plt.title("🔗 Feature Correlation Matrix", fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()
    
    # Target correlations
    target_corr = corr_matrix[target_col].drop(target_col).sort_values(key=abs, ascending=False)
    
    print("🎯 TARGET VARIABLE CORRELATIONS")
    print("=" * 35)
    for feature, corr in target_corr.items():
        direction = "↗️" if corr > 0 else "↘️" if corr < 0 else "➡️"
        strength = "Strong" if abs(corr) > 0.7 else "Moderate" if abs(corr) > 0.3 else "Weak"
        print(f"{direction} {feature}: {corr:.3f} ({strength})")
    
    # High correlation pairs
    high_corr_pairs = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.8:
                high_corr_pairs.append({
                    'Feature_1': corr_matrix.columns[i],
                    'Feature_2': corr_matrix.columns[j],
                    'Correlation': corr_val
                })
    
    if high_corr_pairs:
        print(f"\n⚠️ HIGHLY CORRELATED PAIRS (|r| > 0.8):")
        for pair in high_corr_pairs:
            print(f"  {pair['Feature_1']} ↔️ {pair['Feature_2']}: {pair['Correlation']:.3f}")
    else:
        print(f"\n✅ No highly correlated feature pairs found.")

# Analyze numeric features vs target
if len(numeric_cols) > 0:
    n_cols = min(3, len(numeric_cols))
    n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes]
    
    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            sns.boxplot(data=df, x=target_col, y=col, ax=axes[i])
            axes[i].set_title(f"📊 {col} vs Late Billing", fontweight='bold')
            axes[i].set_xlabel("Late Billed (0=No, 1=Yes)")
            axes[i].grid(True, alpha=0.3)
            
            # Add statistical significance
            try:
                from scipy.stats import mannwhitneyu
                group0 = df[df[target_col] == 0][col].dropna()
                group1 = df[df[target_col] == 1][col].dropna()
                if len(group0) > 0 and len(group1) > 0:
                    stat, p_val = mannwhitneyu(group0, group1, alternative='two-sided')
                    significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
                    axes[i].text(0.02, 0.98, f'p={p_val:.3f} {significance}', 
                               transform=axes[i].transAxes, va='top', 
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            except:
                pass
    
    # Hide empty subplots
    for i in range(len(numeric_cols), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle("📊 Numeric Features vs Target Variable", fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()

# Prepare features and target (existing approach)
X = df.drop(target_col, axis=1)
y = df[target_col]

print(f"🎯 Features shape: {X.shape}")
print(f"🎯 Target shape: {y.shape}")
print(f"🎯 Feature count: {len(X.columns)}")

# Balance classes using SMOTE + Under-Sampling (existing approach)
print("⚖️ BALANCING DATASET")
print("=" * 25)

print(f"Original class distribution:")
original_dist = y.value_counts()
print(original_dist)
print(f"Ratio: {original_dist.iloc[0]/original_dist.iloc[1]:.2f}:1")

over = SMOTE(sampling_strategy=0.5, random_state=42)
under = RandomUnderSampler(sampling_strategy=0.5, random_state=42)
steps = [('o', over), ('u', under)]
pipeline = Pipeline(steps=steps)

X_bal, y_bal = pipeline.fit_resample(X, y)

print(f"\nBalanced class distribution:")
balanced_dist = pd.Series(y_bal).value_counts()
print(balanced_dist)
print(f"Ratio: {balanced_dist.iloc[0]/balanced_dist.iloc[1]:.2f}:1")
print(f"✅ Dataset balanced successfully!")

# Train/Test Split (existing approach)
X_train, X_test, y_train, y_test = train_test_split(
    X_bal, y_bal, test_size=0.3, random_state=42, stratify=y_bal
)

print(f"📚 Training set size: {X_train.shape}")
print(f"🧪 Test set size: {X_test.shape}")
print(f"📊 Train target distribution: {pd.Series(y_train).value_counts().tolist()}")
print(f"📊 Test target distribution: {pd.Series(y_test).value_counts().tolist()}")

# Train Models (existing approach)
print("🤖 TRAINING MODELS")
print("=" * 20)

# Logistic Regression
print("\n🎯 Training Logistic Regression...")
lr = LogisticRegression(max_iter=1000, random_state=42)
lr.fit(X_train, y_train)
print("✅ Logistic Regression trained!")

# Random Forest
print("\n🌲 Training Random Forest...")
rf = RandomForestClassifier(n_estimators=100, random_state=42)
rf.fit(X_train, y_train)
print("✅ Random Forest trained!")

print("\n🎉 All models trained successfully!")

# Enhanced evaluation function
def enhanced_evaluate(model, name="Model"):
    """Enhanced model evaluation with comprehensive metrics"""
    print(f"\n📈 {name.upper()} RESULTS")
    print("=" * (len(name) + 15))
    
    # Predictions
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)[:, 1]
    
    # Confusion Matrix
    cm = confusion_matrix(y_test, y_pred)
    print("🔍 Confusion Matrix:")
    print(cm)
    
    # Classification Report
    print("\n📊 Classification Report:")
    print(classification_report(y_test, y_pred))
    
    # AUC Score
    auc_score = roc_auc_score(y_test, y_prob)
    print(f"🎯 AUC Score: {auc_score:.4f}")
    
    # Visualizations
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    
    # Confusion Matrix Heatmap
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['On-time', 'Late'], 
                yticklabels=['On-time', 'Late'], ax=axes[0])
    axes[0].set_title(f'🔍 {name}\nConfusion Matrix', fontweight='bold')
    axes[0].set_xlabel('Predicted')
    axes[0].set_ylabel('Actual')
    
    # ROC Curve
    fpr, tpr, _ = roc_curve(y_test, y_prob)
    axes[1].plot(fpr, tpr, linewidth=2, label=f'{name} (AUC = {auc_score:.3f})')
    axes[1].plot([0, 1], [0, 1], 'k--', alpha=0.5)
    axes[1].set_xlabel('False Positive Rate')
    axes[1].set_ylabel('True Positive Rate')
    axes[1].set_title(f'📈 {name}\nROC Curve', fontweight='bold')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Prediction Distribution
    axes[2].hist(y_prob[y_test == 0], bins=30, alpha=0.7, label='On-time', color='green')
    axes[2].hist(y_prob[y_test == 1], bins=30, alpha=0.7, label='Late', color='red')
    axes[2].set_xlabel('Predicted Probability')
    axes[2].set_ylabel('Frequency')
    axes[2].set_title(f'📊 {name}\nPrediction Distribution', fontweight='bold')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return auc_score, y_pred, y_prob

# Evaluate models
lr_auc, lr_pred, lr_prob = enhanced_evaluate(lr, "Logistic Regression")
rf_auc, rf_pred, rf_prob = enhanced_evaluate(rf, "Random Forest")

# Model Comparison
print("🏆 MODEL COMPARISON")
print("=" * 20)

comparison_data = {
    'Model': ['Logistic Regression', 'Random Forest'],
    'AUC Score': [lr_auc, rf_auc]
}

comparison_df = pd.DataFrame(comparison_data)
display(comparison_df)

best_model = 'Random Forest' if rf_auc > lr_auc else 'Logistic Regression'
best_score = max(lr_auc, rf_auc)
print(f"\n🥇 Best Model: {best_model} (AUC: {best_score:.4f})")

# Side-by-side ROC comparison
plt.figure(figsize=(10, 8))
fpr_lr, tpr_lr, _ = roc_curve(y_test, lr_prob)
fpr_rf, tpr_rf, _ = roc_curve(y_test, rf_prob)

plt.plot(fpr_lr, tpr_lr, linewidth=2, label=f"Logistic Regression (AUC = {lr_auc:.3f})")
plt.plot(fpr_rf, tpr_rf, linewidth=2, label=f"Random Forest (AUC = {rf_auc:.3f})")
plt.plot([0, 1], [0, 1], 'k--', alpha=0.5, label='Random Classifier')

plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('🏆 Model Comparison - ROC Curves', fontsize=14, fontweight='bold')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Feature Importance Analysis
print("🔍 FEATURE IMPORTANCE ANALYSIS")
print("=" * 35)

# Random Forest Feature Importance
rf_importances = rf.feature_importances_
rf_feature_imp = pd.DataFrame({
    'Feature': X.columns,
    'Importance': rf_importances
}).sort_values('Importance', ascending=False)

print("🌲 Random Forest Feature Importance:")
display(rf_feature_imp.head(15))

# Visualize feature importance
fig, axes = plt.subplots(1, 2, figsize=(20, 8))

# Random Forest importance
top_rf_features = rf_feature_imp.head(15)
sns.barplot(data=top_rf_features, x='Importance', y='Feature', ax=axes[0])
axes[0].set_title('🌲 Random Forest\nTop 15 Feature Importances', fontweight='bold')
axes[0].grid(True, alpha=0.3)

# Logistic Regression coefficients
lr_coef = np.abs(lr.coef_[0])
lr_feature_imp = pd.DataFrame({
    'Feature': X.columns,
    'Abs_Coefficient': lr_coef
}).sort_values('Abs_Coefficient', ascending=False)

top_lr_features = lr_feature_imp.head(15)
sns.barplot(data=top_lr_features, x='Abs_Coefficient', y='Feature', ax=axes[1])
axes[1].set_title('🎯 Logistic Regression\nTop 15 Feature Importances', fontweight='bold')
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# SHAP Analysis (existing approach)
print("🧠 SHAP EXPLAINABILITY ANALYSIS")
print("=" * 35)

# Create SHAP explainer for Random Forest
explainer = shap.TreeExplainer(rf)
shap_values = explainer.shap_values(X_test)

# Take SHAP values for positive class
shap_values_positive = shap_values[1] if isinstance(shap_values, list) else shap_values

print("📊 Generating SHAP visualizations...")

# SHAP Summary Plot (Bar)
plt.figure(figsize=(10, 8))
shap.summary_plot(shap_values_positive, X_test, plot_type="bar", show=False)
plt.title('🧠 SHAP Feature Importance', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

# SHAP Summary Plot (Detailed)
plt.figure(figsize=(10, 8))
shap.summary_plot(shap_values_positive, X_test, show=False)
plt.title('🧠 SHAP Summary Plot\n(Feature Impact on Predictions)', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

print("✅ SHAP analysis completed!")

# Generate comprehensive summary
print("📊 ANALYSIS SUMMARY")
print("=" * 25)

# Key findings
late_billing_rate = (df[target_col].sum() / len(df)) * 100
top_3_features = rf_feature_imp.head(3)['Feature'].tolist()

print("🔍 KEY FINDINGS:")
print("-" * 20)
print(f"📊 Dataset size: {df.shape[0]:,} records, {df.shape[1]} features")
print(f"📈 Late billing rate: {late_billing_rate:.1f}%")
print(f"⚖️ Class imbalance: {imbalance_ratio:.1f}:1")
print(f"🎯 Best model: {best_model} (AUC: {best_score:.3f})")
print(f"🔍 Top predictive features: {', '.join(top_3_features)}")

print("\n💡 BUSINESS RECOMMENDATIONS:")
print("-" * 30)
print("1. 🎯 Focus on monitoring the top predictive features identified")
print("2. 📈 Implement early warning system using the trained model")
print("3. 🔍 Investigate root causes for high-risk billing patterns")
print("4. 📊 Regular model retraining to maintain performance")
print("5. 🚀 Deploy model for real-time late billing prediction")

print("\n📈 NEXT STEPS:")
print("-" * 15)
print("• Validate model on new data")
print("• Create monitoring dashboard")
print("• Establish feedback loop")
print("• Document identified risk factors")

print("\n✅ Analysis completed successfully!")