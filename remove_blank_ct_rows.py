#!/usr/bin/env python3
"""
Remove Rows with Blank CT Values Script
======================================
Remove rows (cases) that have blank/missing values in CT (Cycle Time) columns.

Usage: python remove_blank_ct_rows.py
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def remove_rows_with_blank_ct_columns(df, ct_columns=None, strategy='any'):
    """
    Remove rows (cases) that have blank values in CT (Cycle Time) columns.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Input dataframe
    ct_columns : list, optional
        List of CT column names. If None, auto-detect columns with 'CT' in name
    strategy : str, default='any'
        'any': Remove rows with ANY missing CT values
        'all': Remove rows with ALL missing CT values
        'majority': Remove rows with missing values in majority of CT columns
        
    Returns:
    --------
    pd.DataFrame
        Dataframe with rows containing blank CT values removed
    """
    
    # Auto-detect CT columns if not provided
    if ct_columns is None:
        ct_columns = [col for col in df.columns if 'CT_' in col or 'CT->' in col or 'cycle' in col.lower() or 'time' in col.lower()]
        
        # Also look for specific patterns
        ct_patterns = ['Days', 'Time', 'Duration', 'Period']
        for pattern in ct_patterns:
            potential_cols = [col for col in df.columns if pattern in col and 'CT' in col.upper()]
            ct_columns.extend([col for col in potential_cols if col not in ct_columns])
    
    print(f"🔍 Identifying CT (Cycle Time) columns...")
    print(f"📊 Found {len(ct_columns)} CT-related columns:")
    
    if not ct_columns:
        print("❌ No CT columns found! Looking for any columns with 'Days' or 'Time'...")
        # Fallback: look for any columns that might be cycle times
        fallback_columns = [col for col in df.columns if 'Days' in col or 'Time' in col]
        if fallback_columns:
            print(f"🔍 Found {len(fallback_columns)} potential time-related columns:")
            for i, col in enumerate(fallback_columns, 1):
                missing_count = df[col].isnull().sum()
                missing_pct = (missing_count / len(df)) * 100
                print(f"   {i:2d}. {col} ({missing_count:,} missing, {missing_pct:.1f}%)")
            
            use_fallback = input(f"\nUse these columns as CT columns? (y/n): ").strip().lower()
            if use_fallback == 'y':
                ct_columns = fallback_columns
            else:
                print("❌ No CT columns to process. Returning original dataframe.")
                return df, pd.DataFrame()
        else:
            print("❌ No time-related columns found! Returning original dataframe.")
            return df, pd.DataFrame()
    
    # Show CT columns found
    for i, col in enumerate(ct_columns, 1):
        missing_count = df[col].isnull().sum()
        missing_pct = (missing_count / len(df)) * 100
        print(f"   {i:2d}. {col} ({missing_count:,} missing, {missing_pct:.1f}%)")
    
    print(f"\n🎯 Strategy: {strategy.upper()}")
    
    # Apply filtering strategy
    original_count = len(df)
    
    if strategy == 'any':
        # Remove rows with ANY missing CT values
        df_cleaned = df.dropna(subset=ct_columns, how='any').copy()
        print(f"   Removing rows with ANY missing values in CT columns")
        
    elif strategy == 'all':
        # Remove rows with ALL missing CT values
        df_cleaned = df.dropna(subset=ct_columns, how='all').copy()
        print(f"   Removing rows with ALL missing values in CT columns")
        
    elif strategy == 'majority':
        # Remove rows with missing values in majority of CT columns
        threshold = len(ct_columns) / 2
        df_cleaned = df.dropna(subset=ct_columns, thresh=int(threshold)).copy()
        print(f"   Removing rows with missing values in majority ({threshold:.0f}+) of CT columns")
    
    else:
        print(f"❌ Unknown strategy: {strategy}. Using 'any' strategy.")
        df_cleaned = df.dropna(subset=ct_columns, how='any').copy()
    
    # Calculate removal statistics
    removed_count = original_count - len(df_cleaned)
    removal_percentage = (removed_count / original_count) * 100
    
    print(f"\n📈 Row Removal Results:")
    print(f"   Original rows: {original_count:,}")
    print(f"   Cleaned rows: {len(df_cleaned):,}")
    print(f"   Removed rows: {removed_count:,} ({removal_percentage:.1f}%)")
    print(f"   Data retention: {(len(df_cleaned) / original_count * 100):.1f}%")
    
    # Analyze missing values after cleaning
    print(f"\n📊 Missing values in CT columns after cleaning:")
    missing_summary = []
    for col in ct_columns:
        missing_count = df_cleaned[col].isnull().sum()
        missing_pct = (missing_count / len(df_cleaned)) * 100 if len(df_cleaned) > 0 else 0
        missing_summary.append({
            'Column': col,
            'Missing_Count': missing_count,
            'Missing_Percentage': missing_pct
        })
        print(f"   • {col}: {missing_count} ({missing_pct:.1f}%)")
    
    missing_df = pd.DataFrame(missing_summary)
    
    return df_cleaned, missing_df

def main():
    """Main function to run the row removal script."""
    
    print("="*70)
    print("        REMOVE ROWS WITH BLANK CT VALUES SCRIPT")
    print("="*70)
    
    # Configuration
    INPUT_FILE = "20250613160303_CELONIS_EXPORT.csv"
    
    # Check if input file exists
    if not os.path.exists(INPUT_FILE):
        print(f"❌ Error: Input file '{INPUT_FILE}' not found!")
        print(f"📁 Available CSV files:")
        for file in os.listdir('.'):
            if file.endswith('.csv'):
                print(f"   • {file}")
        return
    
    # Load the data
    print(f"📂 Loading data from: {INPUT_FILE}")
    try:
        df = pd.read_csv(INPUT_FILE)
        print(f"✅ Data loaded successfully!")
        print(f"📊 Original shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Choose strategy
    print(f"\nChoose removal strategy:")
    print("1. Remove rows with ANY missing CT values (strict)")
    print("2. Remove rows with ALL missing CT values (permissive)")
    print("3. Remove rows with missing values in MAJORITY of CT columns (balanced)")
    
    strategy_map = {
        "1": "any",
        "2": "all", 
        "3": "majority"
    }
    
    try:
        choice = input("Enter choice (1, 2, or 3): ").strip()
        strategy = strategy_map.get(choice, "any")
        print(f"🎯 Using {strategy.upper()} strategy")
    except:
        print("🎯 Using ANY strategy as default")
        strategy = "any"
    
    # Remove rows with blank CT values
    df_cleaned, missing_summary = remove_rows_with_blank_ct_columns(df, strategy=strategy)
    
    if df_cleaned is not None and len(df_cleaned) > 0:
        # Generate output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(INPUT_FILE))[0]
        output_file = f"{base_name}_ct_cleaned_{strategy}_{timestamp}.csv"
        
        # Save cleaned data
        try:
            df_cleaned.to_csv(output_file, index=False)
            file_size = os.path.getsize(output_file) / 1024**2
            print(f"\n💾 Cleaned data saved to: {output_file}")
            print(f"📁 File size: {file_size:.2f} MB")
        except Exception as e:
            print(f"❌ Error saving file: {e}")
            return
        
        # Final data quality check
        print(f"\n📊 FINAL DATA QUALITY CHECK:")
        print("="*40)
        
        # Check target variable if exists
        target_candidates = ['IsLate_Billed', 'Late_Billed']
        target_col = None
        for candidate in target_candidates:
            if candidate in df_cleaned.columns:
                target_col = candidate
                break
        
        if target_col:
            target_dist = df_cleaned[target_col].value_counts()
            print(f"🎯 Target variable ({target_col}) distribution:")
            for value, count in target_dist.items():
                pct = (count / len(df_cleaned)) * 100
                print(f"   Value {value}: {count:,} ({pct:.1f}%)")
        
        # Overall missing values
        total_missing = df_cleaned.isnull().sum().sum()
        print(f"📊 Total remaining missing values: {total_missing:,}")
        
        if total_missing > 0:
            print(f"📋 Columns still with missing values:")
            missing_cols = df_cleaned.isnull().sum()
            for col, missing_count in missing_cols[missing_cols > 0].items():
                missing_pct = (missing_count / len(df_cleaned)) * 100
                print(f"   • {col}: {missing_count} ({missing_pct:.1f}%)")
        
        print(f"\n🎉 Row removal completed successfully!")
        print(f"📊 Use '{output_file}' for your EDA and modeling.")
        print(f"📈 Data is now {(len(df_cleaned) / len(df) * 100):.1f}% of original size")
        
    else:
        print(f"\n❌ No data remaining after cleaning!")
        
    print("="*70)

if __name__ == "__main__":
    main()
