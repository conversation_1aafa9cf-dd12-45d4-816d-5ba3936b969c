#!/usr/bin/env python3
"""
Feature Filter Script for O2C Data
==================================
This script filters features from CELONIS export data, keeping only columns 
that have some values present (removing completely empty columns).

Author: Data Analysis Team
Date: June 13, 2025
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def filter_features_with_values(input_file, output_file=None, min_non_null_percentage=0.1):
    """
    Filter features keeping only columns with some values present.
    
    Parameters:
    -----------
    input_file : str
        Path to input CSV file
    output_file : str, optional
        Path to output CSV file (if None, auto-generates name)
    min_non_null_percentage : float, default=0.1
        Minimum percentage of non-null values required to keep a column (0.1 = 10%)
    
    Returns:
    --------
    pd.DataFrame
        Filtered dataframe with only columns having sufficient data
    """
    
    print("="*70)
    print("           FEATURE FILTER SCRIPT FOR O2C DATA")
    print("="*70)
    
    # Load the data
    print(f"📂 Loading data from: {input_file}")
    try:
        df = pd.read_csv(input_file)
        print(f"✅ Data loaded successfully!")
        print(f"📊 Original shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return None
    
    # Clean column names
    print(f"\n🧹 Cleaning column names...")
    original_columns = df.columns.tolist()
    df.columns = df.columns.str.strip()
    cleaned_columns = df.columns.tolist()
    
    if original_columns != cleaned_columns:
        print(f"✅ Column names cleaned (removed spaces)")
    else:
        print(f"ℹ️  Column names already clean")
    
    # Analyze missing values
    print(f"\n🔍 Analyzing missing values...")
    missing_info = []
    
    for col in df.columns:
        total_count = len(df)
        non_null_count = df[col].count()
        null_count = df[col].isnull().sum()
        non_null_percentage = (non_null_count / total_count) * 100
        
        missing_info.append({
            'Column': col,
            'Total_Records': total_count,
            'Non_Null_Count': non_null_count,
            'Null_Count': null_count,
            'Non_Null_Percentage': non_null_percentage,
            'Data_Type': str(df[col].dtype)
        })
    
    # Create missing info DataFrame
    missing_df = pd.DataFrame(missing_info)
    missing_df = missing_df.sort_values('Non_Null_Percentage', ascending=False)
    
    print(f"📋 Missing Values Analysis:")
    print(f"{'Column':<30} {'Non-Null Count':<15} {'Non-Null %':<12} {'Data Type':<15}")
    print(f"{'-'*30} {'-'*15} {'-'*12} {'-'*15}")
    
    for _, row in missing_df.head(10).iterrows():
        col_name = row['Column'][:27] + "..." if len(row['Column']) > 30 else row['Column']
        print(f"{col_name:<30} {row['Non_Null_Count']:<15,} {row['Non_Null_Percentage']:<11.1f}% {row['Data_Type']:<15}")
    
    if len(missing_df) > 10:
        print(f"... and {len(missing_df) - 10} more columns")
    
    # Filter columns based on criteria
    min_non_null_count = int(len(df) * min_non_null_percentage)
    
    print(f"\n🎯 Filtering criteria:")
    print(f"   Minimum non-null percentage: {min_non_null_percentage*100:.1f}%")
    print(f"   Minimum non-null count: {min_non_null_count:,}")
    
    # Apply filter
    columns_to_keep = missing_df[missing_df['Non_Null_Percentage'] >= min_non_null_percentage*100]['Column'].tolist()
    columns_to_remove = missing_df[missing_df['Non_Null_Percentage'] < min_non_null_percentage*100]['Column'].tolist()
    
    print(f"\n📊 Filtering results:")
    print(f"✅ Columns to keep: {len(columns_to_keep)}")
    print(f"❌ Columns to remove: {len(columns_to_remove)}")
    
    if columns_to_remove:
        print(f"\n🗑️  Columns being removed (insufficient data):")
        for i, col in enumerate(columns_to_remove, 1):
            pct = missing_df[missing_df['Column'] == col]['Non_Null_Percentage'].iloc[0]
            print(f"   {i:2d}. {col} ({pct:.1f}% data)")
    
    # Create filtered dataframe
    df_filtered = df[columns_to_keep].copy()
    
    print(f"\n📈 Final results:")
    print(f"   Original: {df.shape[0]:,} rows × {df.shape[1]} columns")
    print(f"   Filtered: {df_filtered.shape[0]:,} rows × {df_filtered.shape[1]} columns")
    print(f"   Columns removed: {df.shape[1] - df_filtered.shape[1]}")
    print(f"   Data retention: {(df_filtered.shape[1] / df.shape[1] * 100):.1f}%")
    
    # Generate output filename if not provided
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = f"{base_name}_filtered_{timestamp}.csv"
    
    # Save filtered data
    try:
        df_filtered.to_csv(output_file, index=False)
        print(f"\n💾 Filtered data saved to: {output_file}")
        print(f"📁 File size: {os.path.getsize(output_file) / 1024**2:.2f} MB")
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return df_filtered
    
    # Generate summary report
    print(f"\n📊 SUMMARY REPORT:")
    print("="*50)
    print(f"Input File: {input_file}")
    print(f"Output File: {output_file}")
    print(f"Filter Threshold: {min_non_null_percentage*100:.1f}% non-null values")
    print(f"Original Columns: {df.shape[1]}")
    print(f"Filtered Columns: {df_filtered.shape[1]}")
    print(f"Columns Removed: {len(columns_to_remove)}")
    print(f"Data Quality Improvement: Removed {len(columns_to_remove)} low-quality columns")
    
    # Feature type analysis of filtered data
    print(f"\n🔍 FILTERED DATA ANALYSIS:")
    print("="*40)
    
    numerical_cols = df_filtered.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df_filtered.select_dtypes(include=['object']).columns.tolist()
    
    print(f"📈 Numerical columns: {len(numerical_cols)}")
    print(f"📝 Categorical columns: {len(categorical_cols)}")
    
    if 'IsLate_Billed' in df_filtered.columns:
        target_dist = df_filtered['IsLate_Billed'].value_counts()
        print(f"🎯 Target distribution: {dict(target_dist)}")
    
    print(f"\n✅ Feature filtering completed successfully!")
    print("="*70)
    
    return df_filtered

def main():
    """
    Main function to run the feature filtering script.
    """
    
    # Configuration
    INPUT_FILE = "20250613160303_CELONIS_EXPORT.csv"
    OUTPUT_FILE = None  # Will auto-generate
    MIN_NON_NULL_PERCENTAGE = 0.05  # Keep columns with at least 5% non-null values
    
    # Check if input file exists
    if not os.path.exists(INPUT_FILE):
        print(f"❌ Error: Input file '{INPUT_FILE}' not found!")
        print(f"📁 Current directory: {os.getcwd()}")
        print(f"📋 Available files:")
        for file in os.listdir('.'):
            if file.endswith('.csv'):
                print(f"   • {file}")
        return
    
    # Run the filtering
    filtered_df = filter_features_with_values(
        input_file=INPUT_FILE,
        output_file=OUTPUT_FILE,
        min_non_null_percentage=MIN_NON_NULL_PERCENTAGE
    )
    
    if filtered_df is not None:
        print(f"\n🎉 Script completed successfully!")
        print(f"📊 Use the filtered dataset for your EDA and modeling.")
    else:
        print(f"\n❌ Script failed!")

if __name__ == "__main__":
    main()
